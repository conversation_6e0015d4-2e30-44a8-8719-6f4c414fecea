<?php

namespace App\Http\Controllers;

use App\Models\ChatRoom;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class ChatRoomController extends Controller
{
    /**
     * Display the dashboard with user's chat rooms.
     */
    public function dashboard()
    {
        $user = Auth::user();
        $userRooms = $user->chatRooms()->with('creator')->get();
        $createdRooms = $user->createdRooms()->get();

        return view('dashboard', compact('userRooms', 'createdRooms'));
    }

    /**
     * Show the form for creating a new chat room.
     */
    public function create()
    {
        return view('chat-rooms.create');
    }

    /**
     * Store a newly created chat room.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'is_private' => 'boolean',
            'password' => 'nullable|string|min:4|required_if:is_private,true',
            'max_members' => 'integer|min:2|max:100',
        ]);

        $chatRoom = ChatRoom::create([
            'name' => $request->name,
            'description' => $request->description,
            'created_by' => Auth::id(),
            'is_private' => $request->boolean('is_private'),
            'password' => $request->is_private ? Hash::make($request->password) : null,
            'max_members' => $request->max_members ?? 50,
        ]);

        // Add creator as admin
        $chatRoom->addMember(Auth::user(), 'admin');

        return redirect()->route('chat-rooms.show', $chatRoom)
                        ->with('success', 'چت روم با موفقیت ایجاد شد!');
    }

    /**
     * Display the specified chat room.
     */
    public function show(ChatRoom $chatRoom)
    {
        // Check if user is a member
        if (!$chatRoom->hasMember(Auth::user())) {
            return redirect()->route('dashboard')
                           ->with('error', 'شما عضو این چت روم نیستید.');
        }

        $messages = $chatRoom->latestMessages(50)->get()->reverse();
        $members = $chatRoom->members()->get();
        $onlineMembers = $chatRoom->onlineMembers()->get();

        return view('chat-rooms.show', compact('chatRoom', 'messages', 'members', 'onlineMembers'));
    }

    /**
     * Show the form for joining a chat room.
     */
    public function showJoin()
    {
        return view('chat-rooms.join');
    }

    /**
     * Join a chat room by room code.
     */
    public function join(Request $request)
    {
        $request->validate([
            'room_code' => 'required|string',
            'password' => 'nullable|string',
        ]);

        $chatRoom = ChatRoom::where('room_code', $request->room_code)
                           ->where('is_active', true)
                           ->first();

        if (!$chatRoom) {
            return back()->withErrors(['room_code' => 'کد روم یافت نشد.']);
        }

        if ($chatRoom->isFull()) {
            return back()->withErrors(['room_code' => 'این روم پر است.']);
        }

        if ($chatRoom->hasMember(Auth::user())) {
            return redirect()->route('chat-rooms.show', $chatRoom);
        }

        // Check password for private rooms
        if ($chatRoom->is_private) {
            if (!$request->password || !Hash::check($request->password, $chatRoom->password)) {
                return back()->withErrors(['password' => 'رمز عبور اشتباه است.']);
            }
        }

        $chatRoom->addMember(Auth::user());

        return redirect()->route('chat-rooms.show', $chatRoom)
                        ->with('success', 'با موفقیت به روم پیوستید!');
    }

    /**
     * Leave a chat room.
     */
    public function leave(ChatRoom $chatRoom)
    {
        if (!$chatRoom->hasMember(Auth::user())) {
            return redirect()->route('dashboard');
        }

        $chatRoom->removeMember(Auth::user());

        return redirect()->route('dashboard')
                        ->with('success', 'از روم خارج شدید.');
    }

    /**
     * Check if room code exists (API endpoint).
     */
    public function checkRoomCode($roomCode)
    {
        $chatRoom = ChatRoom::where('room_code', $roomCode)
                           ->where('is_active', true)
                           ->first();

        if (!$chatRoom) {
            return response()->json([
                'success' => false,
                'message' => 'چت روم یافت نشد'
            ]);
        }

        // Check if room is full
        if ($chatRoom->isFull()) {
            return response()->json([
                'success' => false,
                'message' => 'این چت روم پر است'
            ]);
        }

        // Check if user is already a member
        if ($chatRoom->hasMember(Auth::user())) {
            return response()->json([
                'success' => false,
                'message' => 'شما قبلاً عضو این روم هستید'
            ]);
        }

        return response()->json([
            'success' => true,
            'room' => [
                'id' => $chatRoom->id,
                'name' => $chatRoom->name,
                'description' => $chatRoom->description,
                'is_private' => $chatRoom->is_private,
                'member_count' => $chatRoom->member_count,
                'max_members' => $chatRoom->max_members,
            ]
        ]);
    }
}
