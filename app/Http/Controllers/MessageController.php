<?php

namespace App\Http\Controllers;

use App\Models\ChatRoom;
use App\Models\Message;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class MessageController extends Controller
{
    /**
     * Store a new message.
     */
    public function store(Request $request, ChatRoom $chatRoom)
    {
        // Check if user is a member
        if (!$chatRoom->hasMember(Auth::user())) {
            return response()->json(['error' => 'شما عضو این چت روم نیستید.'], 403);
        }

        $request->validate([
            'content' => 'required|string|max:2000',
            'reply_to' => 'nullable|exists:messages,id',
        ]);

        $message = Message::create([
            'user_id' => Auth::id(),
            'chat_room_id' => $chatRoom->id,
            'content' => $request->content,
            'reply_to' => $request->reply_to,
        ]);

        $message->load('user', 'replyTo.user');

        return response()->json([
            'success' => true,
            'message' => $message,
        ]);
    }

    /**
     * Get messages for a chat room.
     */
    public function index(ChatRoom $chatRoom, Request $request)
    {
        // Check if user is a member
        if (!$chatRoom->hasMember(Auth::user())) {
            return response()->json(['error' => 'شما عضو این چت روم نیستید.'], 403);
        }

        $afterId = $request->get('after', 0);

        if ($afterId > 0) {
            // Get new messages after specific ID
            $messages = $chatRoom->messages()
                               ->with('user', 'replyTo.user')
                               ->where('id', '>', $afterId)
                               ->where('is_deleted', false)
                               ->orderBy('created_at', 'asc')
                               ->get();
        } else {
            // Get latest messages for initial load
            $page = $request->get('page', 1);
            $limit = 50;
            $offset = ($page - 1) * $limit;

            $messages = $chatRoom->messages()
                               ->with('user', 'replyTo.user')
                               ->where('is_deleted', false)
                               ->orderBy('created_at', 'desc')
                               ->offset($offset)
                               ->limit($limit)
                               ->get()
                               ->reverse()
                               ->values();
        }

        return response()->json([
            'messages' => $messages,
            'has_more' => $afterId == 0 ? $chatRoom->messages()->where('is_deleted', false)->count() > (($request->get('page', 1)) * 50) : false,
        ]);
    }

    /**
     * Update a message.
     */
    public function update(Request $request, Message $message)
    {
        // Check if user owns the message
        if ($message->user_id !== Auth::id()) {
            return response()->json(['error' => 'شما نمی‌توانید این پیام را ویرایش کنید.'], 403);
        }

        // Check if message is not too old (e.g., 15 minutes)
        if ($message->created_at->diffInMinutes(now()) > 15) {
            return response()->json(['error' => 'زمان ویرایش این پیام گذشته است.'], 403);
        }

        $request->validate([
            'content' => 'required|string|max:2000',
        ]);

        $message->update(['content' => $request->content]);
        $message->markAsEdited();

        $message->load('user', 'replyTo.user');

        return response()->json([
            'success' => true,
            'message' => $message,
        ]);
    }

    /**
     * Delete a message.
     */
    public function destroy(Message $message)
    {
        // Check if user owns the message or is admin/moderator
        $roomMember = $message->chatRoom->members()
                             ->where('user_id', Auth::id())
                             ->first();

        if ($message->user_id !== Auth::id() && 
            (!$roomMember || !$roomMember->pivot->hasModeratorPrivileges())) {
            return response()->json(['error' => 'شما نمی‌توانید این پیام را حذف کنید.'], 403);
        }

        $message->softDelete();

        return response()->json(['success' => true]);
    }

    /**
     * Upload file and send as message.
     */
    public function uploadFile(Request $request, ChatRoom $chatRoom)
    {
        // Check if user is a member
        if (!$chatRoom->hasMember(Auth::user())) {
            return response()->json(['error' => 'شما عضو این چت روم نیستید.'], 403);
        }

        $request->validate([
            'file' => 'required|file|max:10240', // 10MB max
        ]);

        $file = $request->file('file');
        $fileName = time() . '_' . $file->getClientOriginalName();
        $filePath = $file->storeAs('chat-files', $fileName, 'public');

        $message = Message::create([
            'user_id' => Auth::id(),
            'chat_room_id' => $chatRoom->id,
            'content' => 'فایل ارسال شد: ' . $file->getClientOriginalName(),
            'type' => $this->getFileType($file),
            'file_path' => $filePath,
            'file_name' => $file->getClientOriginalName(),
            'file_size' => $file->getSize(),
        ]);

        $message->load('user');

        return response()->json([
            'success' => true,
            'message' => $message,
        ]);
    }

    /**
     * Determine file type based on extension.
     */
    private function getFileType($file): string
    {
        $extension = strtolower($file->getClientOriginalExtension());
        
        if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
            return 'image';
        }
        
        return 'file';
    }
}
