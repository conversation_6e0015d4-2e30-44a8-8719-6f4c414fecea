<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'avatar',
        'is_online',
        'last_seen',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'last_seen' => 'datetime',
            'is_online' => 'boolean',
        ];
    }

    /**
     * Get the chat rooms that the user is a member of.
     */
    public function chatRooms()
    {
        return $this->belongsToMany(ChatRoom::class, 'room_members')
                    ->withPivot(['role', 'joined_at', 'last_read_at', 'is_muted'])
                    ->withTimestamps();
    }

    /**
     * Get the messages sent by the user.
     */
    public function messages()
    {
        return $this->hasMany(Message::class);
    }

    /**
     * Get the chat rooms created by the user.
     */
    public function createdRooms()
    {
        return $this->hasMany(ChatRoom::class, 'created_by');
    }

    /**
     * Check if user is online.
     */
    public function isOnline(): bool
    {
        return $this->is_online && $this->last_seen && $this->last_seen->diffInMinutes(now()) < 5;
    }

    /**
     * Mark user as online.
     */
    public function markAsOnline(): void
    {
        $this->update([
            'is_online' => true,
            'last_seen' => now(),
        ]);
    }

    /**
     * Mark user as offline.
     */
    public function markAsOffline(): void
    {
        $this->update([
            'is_online' => false,
            'last_seen' => now(),
        ]);
    }
}
