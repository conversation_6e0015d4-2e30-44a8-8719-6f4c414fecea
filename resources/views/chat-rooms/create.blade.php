@extends('layouts.app')

@section('title', 'ایجاد چت روم جدید')

@section('content')
<div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="py-6">
        <div class="md:flex md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                    ایجاد چت روم جدید
                </h2>
            </div>
            <div class="mt-4 flex md:mt-0 md:mr-4">
                <a href="{{ route('dashboard') }}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    بازگشت به داشبورد
                </a>
            </div>
        </div>
    </div>

    <div class="bg-white shadow rounded-lg">
        <form action="{{ route('chat-rooms.store') }}" method="POST" class="space-y-6 p-6">
            @csrf
            
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700">نام چت روم</label>
                <input type="text" name="name" id="name" required
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('name') border-red-500 @enderror"
                       placeholder="نام چت روم را وارد کنید"
                       value="{{ old('name') }}">
                @error('name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="description" class="block text-sm font-medium text-gray-700">توضیحات (اختیاری)</label>
                <textarea name="description" id="description" rows="3"
                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('description') border-red-500 @enderror"
                          placeholder="توضیحات کوتاهی درباره چت روم">{{ old('description') }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="max_members" class="block text-sm font-medium text-gray-700">حداکثر تعداد اعضا</label>
                <input type="number" name="max_members" id="max_members" min="2" max="100"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('max_members') border-red-500 @enderror"
                       placeholder="50"
                       value="{{ old('max_members', 50) }}">
                @error('max_members')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
                <p class="mt-1 text-sm text-gray-500">بین ۲ تا ۱۰۰ عضو</p>
            </div>

            <div class="flex items-start">
                <div class="flex items-center h-5">
                    <input id="is_private" name="is_private" type="checkbox" value="1"
                           class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
                           {{ old('is_private') ? 'checked' : '' }}
                           onchange="togglePasswordField()">
                </div>
                <div class="mr-3 text-sm">
                    <label for="is_private" class="font-medium text-gray-700">چت روم خصوصی</label>
                    <p class="text-gray-500">برای ورود به این روم نیاز به رمز عبور است</p>
                </div>
            </div>

            <div id="password-field" class="hidden">
                <label for="password" class="block text-sm font-medium text-gray-700">رمز عبور چت روم</label>
                <input type="password" name="password" id="password"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('password') border-red-500 @enderror"
                       placeholder="رمز عبور (حداقل ۴ کاراکتر)">
                @error('password')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="flex justify-end space-x-3 space-x-reverse">
                <a href="{{ route('dashboard') }}" 
                   class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    انصراف
                </a>
                <button type="submit" 
                        class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    ایجاد چت روم
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function togglePasswordField() {
    const checkbox = document.getElementById('is_private');
    const passwordField = document.getElementById('password-field');
    const passwordInput = document.getElementById('password');
    
    if (checkbox.checked) {
        passwordField.classList.remove('hidden');
        passwordInput.required = true;
    } else {
        passwordField.classList.add('hidden');
        passwordInput.required = false;
        passwordInput.value = '';
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    togglePasswordField();
});
</script>
@endsection
