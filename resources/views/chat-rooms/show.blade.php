@extends('layouts.app')

@section('title', $chatRoom->name)

@section('content')
<div class="h-screen flex flex-col bg-gradient-to-br from-indigo-50 via-white to-purple-50">
    <!-- Chat Header with Glass Effect -->
    <div class="bg-white/80 backdrop-blur-lg border-b border-white/20 px-4 py-4 sm:px-6 shadow-lg">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4 space-x-reverse">
                <a href="{{ route('dashboard') }}"
                   class="group p-2 text-gray-400 hover:text-indigo-600 rounded-xl hover:bg-indigo-50 transition-all duration-200">
                    <svg class="h-6 w-6 transform group-hover:-translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>

                <div class="flex items-center space-x-3 space-x-reverse">
                    <div class="relative">
                        <div class="h-12 w-12 rounded-2xl bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center shadow-lg">
                            <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                        </div>
                        <div class="absolute -bottom-1 -right-1 h-4 w-4 bg-green-400 border-2 border-white rounded-full animate-pulse"></div>
                    </div>

                    <div>
                        <h1 class="text-xl font-bold text-gray-900 flex items-center">
                            {{ $chatRoom->name }}
                            @if($chatRoom->is_private)
                                <svg class="h-4 w-4 text-amber-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                </svg>
                            @endif
                        </h1>
                        <div class="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
                            <span class="flex items-center">
                                <svg class="h-4 w-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                                {{ $members->count() }} عضو
                            </span>
                            <span>•</span>
                            <span class="flex items-center">
                                <div class="h-2 w-2 bg-green-400 rounded-full ml-1 animate-pulse"></div>
                                <span id="online-count">{{ $onlineMembers->count() }}</span> آنلاین
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex items-center space-x-2 space-x-reverse">
                <button type="button" onclick="toggleRoomInfo()"
                        class="group p-3 text-gray-400 hover:text-indigo-600 rounded-xl hover:bg-indigo-50 transition-all duration-200 hover:scale-105">
                    <svg class="h-5 w-5 group-hover:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </button>

                <button type="button" onclick="toggleMembersList()"
                        class="group p-3 text-gray-400 hover:text-purple-600 rounded-xl hover:bg-purple-50 transition-all duration-200 hover:scale-105">
                    <svg class="h-5 w-5 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </button>

                <button type="button" onclick="toggleEmojiPicker()"
                        class="group p-3 text-gray-400 hover:text-yellow-500 rounded-xl hover:bg-yellow-50 transition-all duration-200 hover:scale-105">
                    <svg class="h-5 w-5 group-hover:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <div class="flex-1 flex overflow-hidden">
        <!-- Main Chat Area -->
        <div class="flex-1 flex flex-col relative">
            <!-- Messages Container with Custom Scrollbar -->
            <div id="messages-container" class="flex-1 overflow-y-auto p-6 space-y-6 scroll-smooth" style="scrollbar-width: thin; scrollbar-color: #e5e7eb #f9fafb;">
                <!-- Welcome Message -->
                <div class="flex justify-center">
                    <div class="bg-white/60 backdrop-blur-sm rounded-2xl px-6 py-3 shadow-sm border border-white/20">
                        <p class="text-sm text-gray-600 text-center">
                            🎉 به چت روم <strong>{{ $chatRoom->name }}</strong> خوش آمدید!
                        </p>
                    </div>
                </div>

                @foreach($messages as $message)
                    <div class="message-item animate-fade-in" data-message-id="{{ $message->id }}">
                        @include('partials.message', ['message' => $message])
                    </div>
                @endforeach

                <!-- Typing Indicator -->
                <div id="typing-indicator" class="hidden flex items-center space-x-2 space-x-reverse px-4">
                    <div class="flex space-x-1 space-x-reverse">
                        <div class="h-2 w-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div class="h-2 w-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                        <div class="h-2 w-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                    </div>
                    <span class="text-sm text-gray-500">در حال تایپ...</span>
                </div>
            </div>

            <!-- Message Input Area with Glass Effect -->
            <div class="bg-white/80 backdrop-blur-lg border-t border-white/20 p-6">
                <!-- Reply Preview with Animation -->
                <div id="reply-preview" class="hidden mb-4 transform transition-all duration-300 ease-out">
                    <div class="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-2xl p-4 border-r-4 border-indigo-500 shadow-sm">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <p class="text-sm font-medium text-indigo-600 mb-1">پاسخ به:</p>
                                <p id="reply-content" class="text-sm text-gray-700 line-clamp-2"></p>
                            </div>
                            <button onclick="cancelReply()" class="group p-1 text-gray-400 hover:text-red-500 rounded-lg transition-colors duration-200">
                                <svg class="h-4 w-4 group-hover:rotate-90 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Emoji Picker -->
                <div id="emoji-picker" class="hidden mb-4 transform transition-all duration-300 ease-out">
                    <div class="bg-white rounded-2xl shadow-lg border border-gray-200 p-4">
                        <div class="grid grid-cols-8 gap-2">
                            <button onclick="insertEmoji('😀')" class="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200">😀</button>
                            <button onclick="insertEmoji('😂')" class="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200">😂</button>
                            <button onclick="insertEmoji('😍')" class="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200">😍</button>
                            <button onclick="insertEmoji('🤔')" class="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200">🤔</button>
                            <button onclick="insertEmoji('👍')" class="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200">👍</button>
                            <button onclick="insertEmoji('❤️')" class="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200">❤️</button>
                            <button onclick="insertEmoji('🔥')" class="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200">🔥</button>
                            <button onclick="insertEmoji('💯')" class="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200">💯</button>
                            <button onclick="insertEmoji('😎')" class="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200">😎</button>
                            <button onclick="insertEmoji('🎉')" class="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200">🎉</button>
                            <button onclick="insertEmoji('🚀')" class="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200">🚀</button>
                            <button onclick="insertEmoji('⭐')" class="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200">⭐</button>
                            <button onclick="insertEmoji('💪')" class="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200">💪</button>
                            <button onclick="insertEmoji('🙏')" class="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200">🙏</button>
                            <button onclick="insertEmoji('✨')" class="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200">✨</button>
                            <button onclick="insertEmoji('🌟')" class="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200">🌟</button>
                        </div>
                    </div>
                </div>

                <form id="message-form" class="flex items-end space-x-3 space-x-reverse">
                    @csrf
                    <input type="hidden" id="reply-to" name="reply_to" value="">

                    <!-- Message Input with Modern Design -->
                    <div class="flex-1 relative">
                        <div class="relative">
                            <textarea id="message-input"
                                   name="content"
                                   placeholder="پیام خود را بنویسید..."
                                   class="block w-full resize-none border-0 bg-gray-50 rounded-2xl px-4 py-3 pr-12 text-sm placeholder-gray-500 focus:ring-2 focus:ring-indigo-500 focus:bg-white transition-all duration-200 shadow-sm"
                                   rows="1"
                                   maxlength="2000"
                                   autocomplete="off"
                                   style="min-height: 44px; max-height: 120px;"></textarea>

                            <!-- Character Counter -->
                            <div class="absolute bottom-1 left-2 text-xs text-gray-400">
                                <span id="char-count">0</span>/2000
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <!-- File Upload -->
                        <label for="file-input" class="group cursor-pointer p-3 text-gray-400 hover:text-indigo-600 rounded-xl hover:bg-indigo-50 transition-all duration-200 hover:scale-105">
                            <svg class="h-5 w-5 group-hover:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                            </svg>
                        </label>
                        <input type="file" id="file-input" class="hidden" accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt">

                        <!-- Send Button -->
                        <button type="submit"
                                class="group relative inline-flex items-center justify-center p-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <svg class="h-5 w-5 transform group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                            <div class="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 rounded-xl transition-opacity duration-200"></div>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Sidebar (Members List) with Glass Effect -->
        <div id="members-sidebar" class="hidden w-80 bg-white/80 backdrop-blur-lg border-r border-white/20 overflow-y-auto shadow-xl">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-bold text-gray-900 flex items-center">
                        <svg class="h-6 w-6 text-indigo-500 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        اعضای روم
                    </h3>
                    <span class="bg-indigo-100 text-indigo-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                        {{ $members->count() }}
                    </span>
                </div>

                <!-- Online Members -->
                <div class="mb-6">
                    <h4 class="text-sm font-semibold text-green-600 mb-3 flex items-center">
                        <div class="h-2 w-2 bg-green-400 rounded-full ml-2 animate-pulse"></div>
                        آنلاین ({{ $onlineMembers->count() }})
                    </h4>
                    <div class="space-y-3">
                        @foreach($members->where('is_online', true) as $member)
                            <div class="group flex items-center space-x-3 space-x-reverse p-3 rounded-2xl hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 transition-all duration-200 cursor-pointer">
                                <div class="flex-shrink-0 relative">
                                    <div class="h-12 w-12 rounded-2xl bg-gradient-to-br from-green-400 to-emerald-500 flex items-center justify-center shadow-lg">
                                        <span class="text-lg font-bold text-white">
                                            {{ substr($member->name, 0, 1) }}
                                        </span>
                                    </div>
                                    <div class="absolute -bottom-1 -left-1 h-4 w-4 bg-green-400 border-2 border-white rounded-full animate-pulse"></div>
                                    @if($member->pivot->role === 'admin')
                                        <div class="absolute -top-1 -right-1 h-5 w-5 bg-blue-500 rounded-full flex items-center justify-center">
                                            <svg class="h-3 w-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM15.657 6.343a1 1 0 011.414 0A9.972 9.972 0 0119 12a9.972 9.972 0 01-1.929 5.657 1 1 0 11-1.414-1.414A7.971 7.971 0 0017 12c0-1.636-.491-3.154-1.343-4.243a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                    @endif
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center justify-between">
                                        <p class="text-sm font-semibold text-gray-900 truncate">
                                            {{ $member->name }}
                                        </p>
                                        @if($member->pivot->role === 'admin')
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                👑 مدیر
                                            </span>
                                        @elseif($member->pivot->role === 'moderator')
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                🛡️ ناظر
                                            </span>
                                        @endif
                                    </div>
                                    <p class="text-xs text-green-600 font-medium">
                                        🟢 آنلاین
                                    </p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Offline Members -->
                @if($members->where('is_online', false)->count() > 0)
                <div>
                    <h4 class="text-sm font-semibold text-gray-500 mb-3 flex items-center">
                        <div class="h-2 w-2 bg-gray-400 rounded-full ml-2"></div>
                        آفلاین ({{ $members->where('is_online', false)->count() }})
                    </h4>
                    <div class="space-y-2">
                        @foreach($members->where('is_online', false) as $member)
                            <div class="flex items-center space-x-3 space-x-reverse p-3 rounded-2xl hover:bg-gray-50 transition-all duration-200">
                                <div class="flex-shrink-0 relative">
                                    <div class="h-10 w-10 rounded-xl bg-gray-300 flex items-center justify-center">
                                        <span class="text-sm font-medium text-gray-600">
                                            {{ substr($member->name, 0, 1) }}
                                        </span>
                                    </div>
                                    @if($member->pivot->role === 'admin')
                                        <div class="absolute -top-1 -right-1 h-4 w-4 bg-blue-500 rounded-full flex items-center justify-center">
                                            <svg class="h-2 w-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                    @endif
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center justify-between">
                                        <p class="text-sm font-medium text-gray-700 truncate">
                                            {{ $member->name }}
                                        </p>
                                        @if($member->pivot->role === 'admin')
                                            <span class="text-xs text-blue-600">👑</span>
                                        @elseif($member->pivot->role === 'moderator')
                                            <span class="text-xs text-green-600">🛡️</span>
                                        @endif
                                    </div>
                                    <p class="text-xs text-gray-500">
                                        {{ $member->last_seen ? $member->last_seen->diffForHumans() : 'آفلاین' }}
                                    </p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Room Info Modal with Modern Design -->
<div id="room-info-modal" class="hidden fixed inset-0 bg-black/50 backdrop-blur-sm overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
    <div class="relative bg-white/95 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/20 w-full max-w-md transform transition-all duration-300 scale-95 opacity-0" id="modal-content">
        <!-- Header -->
        <div class="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-t-3xl p-6 text-white">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3 space-x-reverse">
                    <div class="h-12 w-12 rounded-2xl bg-white/20 flex items-center justify-center">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold">اطلاعات روم</h3>
                </div>
                <button onclick="toggleRoomInfo()" class="group p-2 hover:bg-white/20 rounded-xl transition-all duration-200">
                    <svg class="h-6 w-6 group-hover:rotate-90 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Content -->
        <div class="p-6 space-y-6">
            <!-- Room Name -->
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-4">
                <label class="block text-sm font-semibold text-indigo-600 mb-2">نام روم</label>
                <p class="text-lg font-bold text-gray-900 flex items-center">
                    {{ $chatRoom->name }}
                    @if($chatRoom->is_private)
                        <svg class="h-5 w-5 text-amber-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                        </svg>
                    @endif
                </p>
            </div>

            @if($chatRoom->description)
            <!-- Description -->
            <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-4">
                <label class="block text-sm font-semibold text-green-600 mb-2">توضیحات</label>
                <p class="text-gray-900">{{ $chatRoom->description }}</p>
            </div>
            @endif

            <!-- Room Code -->
            <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-4">
                <label class="block text-sm font-semibold text-purple-600 mb-2">کد روم</label>
                <div class="flex items-center justify-between bg-white rounded-xl p-3 border border-purple-200">
                    <code class="text-lg font-mono font-bold text-gray-900 tracking-wider">{{ $chatRoom->room_code }}</code>
                    <button onclick="copyRoomCode()" class="group flex items-center space-x-2 space-x-reverse bg-purple-100 hover:bg-purple-200 text-purple-700 px-3 py-2 rounded-lg transition-all duration-200 hover:scale-105">
                        <svg class="h-4 w-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        <span class="text-sm font-medium">کپی</span>
                    </button>
                </div>
            </div>

            <!-- Stats Grid -->
            <div class="grid grid-cols-2 gap-4">
                <!-- Creator -->
                <div class="bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl p-4">
                    <label class="block text-sm font-semibold text-orange-600 mb-2">سازنده</label>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <div class="h-8 w-8 rounded-xl bg-orange-200 flex items-center justify-center">
                            <span class="text-sm font-bold text-orange-700">
                                {{ substr($chatRoom->creator->name, 0, 1) }}
                            </span>
                        </div>
                        <p class="text-sm font-medium text-gray-900">{{ $chatRoom->creator->name }}</p>
                    </div>
                </div>

                <!-- Member Count -->
                <div class="bg-gradient-to-br from-teal-50 to-cyan-50 rounded-2xl p-4">
                    <label class="block text-sm font-semibold text-teal-600 mb-2">اعضا</label>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <div class="h-8 w-8 rounded-xl bg-teal-200 flex items-center justify-center">
                            <svg class="h-4 w-4 text-teal-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <p class="text-sm font-bold text-gray-900">{{ $members->count() }}/{{ $chatRoom->max_members }}</p>
                    </div>
                </div>

                <!-- Room Type -->
                <div class="bg-gradient-to-br from-violet-50 to-purple-50 rounded-2xl p-4">
                    <label class="block text-sm font-semibold text-violet-600 mb-2">نوع روم</label>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <div class="h-8 w-8 rounded-xl bg-violet-200 flex items-center justify-center">
                            @if($chatRoom->is_private)
                                <svg class="h-4 w-4 text-violet-700" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                </svg>
                            @else
                                <svg class="h-4 w-4 text-violet-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            @endif
                        </div>
                        <p class="text-sm font-medium text-gray-900">{{ $chatRoom->is_private ? 'خصوصی' : 'عمومی' }}</p>
                    </div>
                </div>

                <!-- Online Count -->
                <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-4">
                    <label class="block text-sm font-semibold text-green-600 mb-2">آنلاین</label>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <div class="h-8 w-8 rounded-xl bg-green-200 flex items-center justify-center">
                            <div class="h-3 w-3 bg-green-500 rounded-full animate-pulse"></div>
                        </div>
                        <p class="text-sm font-bold text-gray-900">{{ $onlineMembers->count() }} نفر</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<style>
/* Custom Animations and Styles */
@keyframes fade-in {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slide-in-right {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slide-in-left {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes bounce-in {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 5px rgba(99, 102, 241, 0.5); }
    50% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.8), 0 0 30px rgba(99, 102, 241, 0.6); }
}

.animate-fade-in {
    animation: fade-in 0.5s ease-out;
}

.animate-slide-in-right {
    animation: slide-in-right 0.3s ease-out;
}

.animate-slide-in-left {
    animation: slide-in-left 0.3s ease-out;
}

.animate-bounce-in {
    animation: bounce-in 0.6s ease-out;
}

.animate-pulse-glow {
    animation: pulse-glow 2s infinite;
}

/* Custom Scrollbar */
#messages-container::-webkit-scrollbar {
    width: 6px;
}

#messages-container::-webkit-scrollbar-track {
    background: rgba(243, 244, 246, 0.5);
    border-radius: 10px;
}

#messages-container::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #6366f1, #8b5cf6);
    border-radius: 10px;
}

#messages-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #4f46e5, #7c3aed);
}

/* Message Hover Effects */
.message-item:hover {
    transform: translateY(-1px);
    transition: transform 0.2s ease-out;
}

/* Typing Animation */
.typing-dot {
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}

/* Glass Effect */
.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Gradient Text */
.gradient-text {
    background: linear-gradient(45deg, #6366f1, #8b5cf6, #ec4899);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Button Hover Effects */
.btn-hover-effect {
    position: relative;
    overflow: hidden;
}

.btn-hover-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-hover-effect:hover::before {
    left: 100%;
}

/* Modal Animation */
.modal-enter {
    animation: modal-enter 0.3s ease-out;
}

.modal-exit {
    animation: modal-exit 0.3s ease-in;
}

@keyframes modal-enter {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes modal-exit {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.9) translateY(-10px);
    }
}
</style>

<script>
const chatRoomId = {{ $chatRoom->id }};
const currentUserId = {{ auth()->id() }};
const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

document.addEventListener('DOMContentLoaded', function() {
    initializeChat();
    setupAdvancedFeatures();
});

function setupAdvancedFeatures() {
    // Auto-resize textarea
    const messageInput = document.getElementById('message-input');
    messageInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.min(this.scrollHeight, 120) + 'px';

        // Update character counter
        const charCount = document.getElementById('char-count');
        charCount.textContent = this.value.length;

        // Change color based on character count
        if (this.value.length > 1800) {
            charCount.className = 'text-red-500 font-bold';
        } else if (this.value.length > 1500) {
            charCount.className = 'text-yellow-500 font-medium';
        } else {
            charCount.className = 'text-gray-400';
        }
    });

    // Typing indicator
    let typingTimer;
    messageInput.addEventListener('input', function() {
        showTypingIndicator();
        clearTimeout(typingTimer);
        typingTimer = setTimeout(hideTypingIndicator, 1000);
    });

    // Modal animations
    const modal = document.getElementById('room-info-modal');
    const modalContent = document.getElementById('modal-content');

    window.toggleRoomInfo = function() {
        if (modal.classList.contains('hidden')) {
            modal.classList.remove('hidden');
            setTimeout(() => {
                modalContent.classList.remove('scale-95', 'opacity-0');
                modalContent.classList.add('scale-100', 'opacity-100');
            }, 10);
        } else {
            modalContent.classList.remove('scale-100', 'opacity-100');
            modalContent.classList.add('scale-95', 'opacity-0');
            setTimeout(() => {
                modal.classList.add('hidden');
            }, 300);
        }
    };

    // Smooth scroll to bottom
    window.scrollToBottom = function() {
        const container = document.getElementById('messages-container');
        container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth'
        });
    };

    // Enhanced emoji picker
    window.toggleEmojiPicker = function() {
        const picker = document.getElementById('emoji-picker');
        picker.classList.toggle('hidden');
        if (!picker.classList.contains('hidden')) {
            picker.classList.add('animate-bounce-in');
        }
    };

    window.insertEmoji = function(emoji) {
        const input = document.getElementById('message-input');
        const start = input.selectionStart;
        const end = input.selectionEnd;
        const text = input.value;

        input.value = text.substring(0, start) + emoji + text.substring(end);
        input.selectionStart = input.selectionEnd = start + emoji.length;
        input.focus();

        // Trigger input event to update character counter
        input.dispatchEvent(new Event('input'));
    };

    // Enhanced members sidebar
    window.toggleMembersList = function() {
        const sidebar = document.getElementById('members-sidebar');
        sidebar.classList.toggle('hidden');
        if (!sidebar.classList.contains('hidden')) {
            sidebar.classList.add('animate-slide-in-right');
        }
    };

    // Copy room code with animation
    window.copyRoomCode = function() {
        const roomCode = '{{ $chatRoom->room_code }}';
        navigator.clipboard.writeText(roomCode).then(() => {
            // Show success animation
            const button = event.target.closest('button');
            const originalText = button.innerHTML;
            button.innerHTML = `
                <svg class="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-sm font-medium text-green-600">کپی شد!</span>
            `;
            button.classList.add('animate-pulse-glow');

            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('animate-pulse-glow');
            }, 2000);
        });
    };
}

function showTypingIndicator() {
    const indicator = document.getElementById('typing-indicator');
    indicator.classList.remove('hidden');
    indicator.classList.add('animate-fade-in');
}

function hideTypingIndicator() {
    const indicator = document.getElementById('typing-indicator');
    indicator.classList.add('hidden');
    indicator.classList.remove('animate-fade-in');
}

// Enhanced message animations
function addMessageWithAnimation(messageElement) {
    messageElement.classList.add('animate-fade-in');
    setTimeout(() => {
        messageElement.classList.remove('animate-fade-in');
    }, 500);
}

// Sound effects (optional)
function playNotificationSound() {
    // You can add sound effects here
    // const audio = new Audio('/sounds/notification.mp3');
    // audio.play().catch(() => {});
}
</script>
<script src="{{ asset('js/chat.js') }}"></script>
@endpush
@endsection
