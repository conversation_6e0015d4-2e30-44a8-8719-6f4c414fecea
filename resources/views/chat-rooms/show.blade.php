@extends('layouts.app')

@section('title', $chatRoom->name)

@section('content')
<!-- Telegram-Style Chat Interface -->
<div class="h-screen flex flex-col bg-telegram-bg relative overflow-hidden">
    <!-- Telegram Background Pattern -->
    <div class="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-100"></div>
    <div class="absolute inset-0 opacity-30" style="background-image: url('data:image/svg+xml,<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><g fill="%23e0e7ff" fill-opacity="0.4"><circle cx="20" cy="20" r="1"/></g></svg>');"></div>

    <!-- Telegram Header -->
    <div class="relative z-10 bg-white shadow-sm border-b border-gray-200">
        <div class="flex items-center px-4 py-3 sm:px-6">
            <!-- Back <PERSON> (Mobile) -->
            <button onclick="goBack()" class="lg:hidden p-2 -ml-2 mr-3 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>

            <!-- Room Avatar & Info -->
            <div class="flex items-center flex-1 min-w-0">
                <div class="relative flex-shrink-0">
                    <div class="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-md">
                        <span class="text-white font-bold text-sm sm:text-base">
                            {{ substr($chatRoom->name, 0, 1) }}
                        </span>
                    </div>
                    <div class="absolute -bottom-0.5 -right-0.5 w-3 h-3 sm:w-4 sm:h-4 bg-green-400 border-2 border-white rounded-full"></div>
                </div>

                <div class="ml-3 flex-1 min-w-0">
                    <div class="flex items-center">
                        <h1 class="text-base sm:text-lg font-semibold text-gray-900 truncate">
                            {{ $chatRoom->name }}
                        </h1>
                        @if($chatRoom->is_private)
                            <svg class="w-4 h-4 text-gray-400 ml-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                            </svg>
                        @endif
                    </div>
                    <p class="text-xs sm:text-sm text-gray-500 truncate">
                        <span id="online-count">{{ $onlineMembers->count() }}</span> از {{ $members->count() }} عضو آنلاین
                    </p>
                </div>
            </div>

            <!-- Header Actions -->
            <div class="flex items-center space-x-1 sm:space-x-2 space-x-reverse">
                <!-- Search Button (Desktop) -->
                <button type="button" onclick="toggleSearch()"
                        class="hidden sm:flex p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-all duration-200">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </button>

                <!-- Members Button -->
                <button type="button" onclick="toggleMembersList()"
                        class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-all duration-200">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </button>

                <!-- More Options -->
                <div class="relative">
                    <button type="button" onclick="toggleMoreOptions()"
                            class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-all duration-200">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                        </svg>
                    </button>

                    <!-- Dropdown Menu -->
                    <div id="more-options-menu" class="hidden absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                        <button onclick="toggleRoomInfo()" class="w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center">
                            <svg class="w-4 h-4 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            اطلاعات روم
                        </button>
                        <button onclick="clearChat()" class="w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center">
                            <svg class="w-4 h-4 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            پاک کردن چت
                        </button>
                        @if($chatRoom->created_by !== auth()->id())
                        <button onclick="leaveRoom()" class="w-full text-right px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center">
                            <svg class="w-4 h-4 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                            </svg>
                            خروج از روم
                        </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Chat Container -->
    <div class="flex-1 flex overflow-hidden relative">
        <!-- Messages Area - Telegram Style -->
        <div class="flex-1 flex flex-col relative">
            <!-- Messages Container -->
            <div id="messages-container" class="flex-1 overflow-y-auto px-4 py-4 space-y-1 scroll-smooth telegram-scrollbar">
                <!-- Date Separator -->
                <div class="flex justify-center my-4">
                    <div class="bg-black/10 backdrop-blur-sm rounded-full px-3 py-1">
                        <span class="text-xs text-gray-600 font-medium">
                            {{ now()->format('d F Y') }}
                        </span>
                    </div>
                </div>

                <!-- Welcome Message -->
                <div class="flex justify-center my-6">
                    <div class="bg-blue-500/10 backdrop-blur-sm rounded-2xl px-4 py-3 max-w-xs text-center">
                        <div class="text-2xl mb-2">🎉</div>
                        <p class="text-sm text-gray-700 font-medium">
                            به <span class="font-bold text-blue-600">{{ $chatRoom->name }}</span> خوش آمدید!
                        </p>
                        <p class="text-xs text-gray-500 mt-1">
                            شما می‌توانید با اعضای این گروه چت کنید
                        </p>
                    </div>
                </div>

                @foreach($messages as $message)
                    <div class="message-item" data-message-id="{{ $message->id }}">
                        @include('partials.telegram-message', ['message' => $message])
                    </div>
                @endforeach

                <!-- Typing Indicator - Telegram Style -->
                <div id="typing-indicator" class="hidden">
                    <div class="flex items-start space-x-2 space-x-reverse mb-2">
                        <div class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center flex-shrink-0">
                            <span class="text-xs font-medium text-gray-600">?</span>
                        </div>
                        <div class="bg-white rounded-2xl rounded-br-md px-4 py-3 shadow-sm max-w-xs">
                            <div class="flex items-center space-x-1 space-x-reverse">
                                <div class="flex space-x-1 space-x-reverse">
                                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                                </div>
                                <span class="text-sm text-gray-500 mr-2">در حال تایپ</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Scroll to Bottom Button -->
                <div id="scroll-to-bottom" class="hidden fixed bottom-24 right-6 z-20">
                    <button onclick="scrollToBottom()" class="w-12 h-12 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-200 hover:scale-105">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Message Input Area - Telegram Style -->
            <div class="bg-white border-t border-gray-100 p-4 relative z-10">
                <!-- Reply Preview - Telegram Style -->
                <div id="reply-preview" class="hidden mb-3 bg-blue-50 border-l-4 border-blue-500 rounded-r-lg p-3 mx-2">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <div class="text-xs font-medium text-blue-600 mb-1">پاسخ به:</div>
                            <div id="reply-content" class="text-sm text-gray-700 line-clamp-2"></div>
                        </div>
                        <button onclick="cancelReply()" class="p-1 text-gray-400 hover:text-gray-600 rounded">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Emoji Picker - Telegram Style -->
                <div id="emoji-picker" class="hidden absolute bottom-full left-4 right-4 mb-2 bg-white rounded-2xl shadow-xl border border-gray-200 p-4 z-50">
                    <div class="grid grid-cols-8 sm:grid-cols-10 gap-2 max-h-48 overflow-y-auto">
                        @php
                            $emojis = ['😀','😃','😄','😁','😆','😅','😂','🤣','😊','😇','🙂','🙃','😉','😌','😍','🥰','😘','😗','😙','😚','😋','😛','😝','😜','🤪','🤨','🧐','🤓','😎','🤩','🥳','😏','😒','😞','😔','😟','😕','🙁','☹️','😣','😖','😫','😩','🥺','😢','😭','😤','😠','😡','🤬','🤯','😳','🥵','🥶','😱','😨','😰','😥','😓','🤗','🤔','🤭','🤫','🤥','😶','😐','😑','😬','🙄','😯','😦','😧','😮','😲','🥱','😴','🤤','😪','😵','🤐','🥴','🤢','🤮','🤧','😷','🤒','🤕','🤑','🤠','😈','👿','👹','👺','🤡','💩','👻','💀','☠️','👽','👾','🤖','🎃','😺','😸','😹','😻','😼','😽','🙀','😿','😾'];
                        @endphp
                        @foreach($emojis as $emoji)
                            <button onclick="insertEmoji('{{ $emoji }}')" class="p-2 hover:bg-gray-100 rounded-lg transition-colors text-lg">{{ $emoji }}</button>
                        @endforeach
                    </div>
                </div>

                <!-- Message Input Form -->
                <form id="message-form" class="flex items-end space-x-2 space-x-reverse">
                    @csrf
                    <input type="hidden" id="reply-to" name="reply_to" value="">

                    <!-- Attachment Button -->
                    <div class="flex-shrink-0">
                        <label for="file-input" class="cursor-pointer p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded-full transition-all duration-200">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                            </svg>
                        </label>
                        <input type="file" id="file-input" class="hidden" accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt">
                    </div>

                    <!-- Message Input Container -->
                    <div class="flex-1 relative bg-gray-50 rounded-3xl border border-gray-200 focus-within:border-blue-300 transition-colors">
                        <div class="flex items-end">
                            <!-- Emoji Button -->
                            <button type="button" onclick="toggleEmojiPicker()" class="p-3 text-gray-400 hover:text-yellow-500 transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </button>

                            <!-- Text Input -->
                            <textarea id="message-input"
                                   name="content"
                                   placeholder="پیام خود را بنویسید..."
                                   class="flex-1 resize-none border-0 bg-transparent px-2 py-3 text-sm placeholder-gray-500 focus:outline-none max-h-32"
                                   rows="1"
                                   maxlength="2000"
                                   autocomplete="off"></textarea>

                            <!-- Voice/Send Button -->
                            <div class="p-2">
                                <button type="submit" id="send-button"
                                        class="w-8 h-8 bg-blue-500 hover:bg-blue-600 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Character Counter -->
                        <div class="absolute bottom-1 left-3 text-xs text-gray-400 hidden" id="char-counter">
                            <span id="char-count">0</span>/2000
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Members Sidebar - Telegram Style -->
        <div id="members-sidebar" class="hidden fixed inset-y-0 right-0 z-50 w-80 bg-white shadow-xl transform transition-transform duration-300 ease-in-out lg:relative lg:translate-x-0 lg:shadow-none lg:border-l lg:border-gray-200">
            <!-- Sidebar Header -->
            <div class="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
                <h3 class="text-lg font-semibold text-gray-900">اعضای روم</h3>
                <div class="flex items-center space-x-2 space-x-reverse">
                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                        {{ $members->count() }}
                    </span>
                    <button onclick="toggleMembersList()" class="lg:hidden p-1 text-gray-400 hover:text-gray-600 rounded">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Members List -->
            <div class="flex-1 overflow-y-auto telegram-scrollbar">
                <!-- Online Members Section -->
                @if($onlineMembers->count() > 0)
                <div class="p-4">
                    <div class="flex items-center mb-3">
                        <div class="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                        <span class="text-sm font-medium text-gray-600">آنلاین — {{ $onlineMembers->count() }}</span>
                    </div>

                    <div class="space-y-1">
                        @foreach($members->where('is_online', true) as $member)
                            <div class="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                                <div class="relative flex-shrink-0">
                                    <div class="w-10 h-10 rounded-full bg-gradient-to-br from-blue-400 to-indigo-500 flex items-center justify-center text-white font-medium">
                                        {{ substr($member->name, 0, 1) }}
                                    </div>
                                    <div class="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-400 border-2 border-white rounded-full"></div>
                                </div>

                                <div class="mr-3 flex-1 min-w-0">
                                    <div class="flex items-center justify-between">
                                        <p class="text-sm font-medium text-gray-900 truncate">
                                            {{ $member->name }}
                                        </p>
                                        @if($member->pivot->role === 'admin')
                                            <span class="text-blue-500 text-xs">👑</span>
                                        @elseif($member->pivot->role === 'moderator')
                                            <span class="text-green-500 text-xs">🛡️</span>
                                        @endif
                                    </div>
                                    <p class="text-xs text-green-600">آنلاین</p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Offline Members Section -->
                @if($members->where('is_online', false)->count() > 0)
                <div class="p-6 {{ $onlineMembers->count() > 0 ? 'border-t border-white/10' : '' }}">
                    <div class="flex items-center mb-4">
                        <div class="w-3 h-3 bg-gray-500 rounded-full mr-3 shadow-lg"></div>
                        <span class="text-sm font-bold text-gray-400">آفلاین — {{ $members->where('is_online', false)->count() }}</span>
                    </div>

                    <div class="space-y-2">
                        @foreach($members->where('is_online', false) as $member)
                            <div class="group flex items-center p-4 rounded-2xl hover:bg-white/5 transition-all duration-300 cursor-pointer">
                                <div class="relative flex-shrink-0">
                                    <div class="w-12 h-12 rounded-2xl bg-gradient-to-br from-gray-500 to-gray-700 flex items-center justify-center text-white font-bold shadow-lg opacity-60">
                                        {{ substr($member->name, 0, 1) }}
                                    </div>
                                </div>

                                <div class="mr-4 flex-1 min-w-0">
                                    <div class="flex items-center justify-between">
                                        <p class="text-sm font-medium text-white/70 truncate group-hover:text-white/90 transition-colors duration-300">
                                            {{ $member->name }}
                                        </p>
                                        @if($member->pivot->role === 'admin')
                                            <div class="flex items-center space-x-1 space-x-reverse bg-blue-500/10 px-2 py-1 rounded-lg">
                                                <span class="text-yellow-400/60 text-sm">👑</span>
                                                <span class="text-xs text-blue-300/60 font-medium">مدیر</span>
                                            </div>
                                        @elseif($member->pivot->role === 'moderator')
                                            <div class="flex items-center space-x-1 space-x-reverse bg-green-500/10 px-2 py-1 rounded-lg">
                                                <span class="text-green-400/60 text-sm">🛡️</span>
                                                <span class="text-xs text-green-300/60 font-medium">ناظر</span>
                                            </div>
                                        @endif
                                    </div>
                                    <p class="text-xs text-gray-500 flex items-center">
                                        <div class="w-2 h-2 bg-gray-500 rounded-full mr-1"></div>
                                        {{ $member->last_seen ? 'آخرین بازدید ' . $member->last_seen->diffForHumans() : 'آفلاین' }}
                                    </p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Sidebar Overlay (Mobile) -->
        <div id="sidebar-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden" onclick="toggleMembersList()"></div>
    </div>
</div>

<!-- Telegram-Style Room Info Modal -->
<div id="room-info-modal" class="hidden fixed inset-0 bg-black/50 backdrop-blur-sm overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
    <div class="relative bg-white rounded-2xl shadow-2xl w-full max-w-md transform transition-all duration-300 scale-95 opacity-0" id="modal-content">
        <!-- Header -->
        <div class="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-t-2xl p-6 text-white">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3 space-x-reverse">
                    <div class="h-12 w-12 rounded-full bg-white/20 flex items-center justify-center">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold">اطلاعات روم</h3>
                </div>
                <button onclick="toggleRoomInfo()" class="group p-2 hover:bg-white/20 rounded-xl transition-all duration-200">
                    <svg class="h-6 w-6 group-hover:rotate-90 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Content -->
        <div class="p-6 space-y-6">
            <!-- Room Name -->
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-4">
                <label class="block text-sm font-semibold text-indigo-600 mb-2">نام روم</label>
                <p class="text-lg font-bold text-gray-900 flex items-center">
                    {{ $chatRoom->name }}
                    @if($chatRoom->is_private)
                        <svg class="h-5 w-5 text-amber-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                        </svg>
                    @endif
                </p>
            </div>

            @if($chatRoom->description)
            <!-- Description -->
            <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-4">
                <label class="block text-sm font-semibold text-green-600 mb-2">توضیحات</label>
                <p class="text-gray-900">{{ $chatRoom->description }}</p>
            </div>
            @endif

            <!-- Room Code -->
            <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-4">
                <label class="block text-sm font-semibold text-purple-600 mb-2">کد روم</label>
                <div class="flex items-center justify-between bg-white rounded-xl p-3 border border-purple-200">
                    <code class="text-lg font-mono font-bold text-gray-900 tracking-wider">{{ $chatRoom->room_code }}</code>
                    <button onclick="copyRoomCode()" class="group flex items-center space-x-2 space-x-reverse bg-purple-100 hover:bg-purple-200 text-purple-700 px-3 py-2 rounded-lg transition-all duration-200 hover:scale-105">
                        <svg class="h-4 w-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        <span class="text-sm font-medium">کپی</span>
                    </button>
                </div>
            </div>

            <!-- Stats Grid -->
            <div class="grid grid-cols-2 gap-4">
                <!-- Creator -->
                <div class="bg-gradient-to-br from-orange-500/20 to-red-500/20 backdrop-blur-xl rounded-3xl p-5 border border-white/10">
                    <div class="flex items-center space-x-2 space-x-reverse mb-3">
                        <div class="w-6 h-6 bg-orange-500/30 rounded-xl flex items-center justify-center">
                            <svg class="w-3 h-3 text-orange-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <label class="text-xs font-bold text-orange-300">سازنده</label>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <div class="h-8 w-8 rounded-2xl bg-gradient-to-br from-orange-400 to-red-500 flex items-center justify-center shadow-lg">
                            <span class="text-sm font-bold text-white">
                                {{ substr($chatRoom->creator->name, 0, 1) }}
                            </span>
                        </div>
                        <p class="text-sm font-bold text-white truncate">{{ $chatRoom->creator->name }}</p>
                    </div>
                </div>

                <!-- Member Count -->
                <div class="bg-gradient-to-br from-teal-500/20 to-cyan-500/20 backdrop-blur-xl rounded-3xl p-5 border border-white/10">
                    <div class="flex items-center space-x-2 space-x-reverse mb-3">
                        <div class="w-6 h-6 bg-teal-500/30 rounded-xl flex items-center justify-center">
                            <svg class="w-3 h-3 text-teal-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <label class="text-xs font-bold text-teal-300">اعضا</label>
                    </div>
                    <p class="text-lg font-bold text-white">{{ $members->count() }}<span class="text-white/60">/{{ $chatRoom->max_members }}</span></p>
                </div>

                <!-- Room Type -->
                <div class="bg-gradient-to-br from-violet-500/20 to-purple-500/20 backdrop-blur-xl rounded-3xl p-5 border border-white/10">
                    <div class="flex items-center space-x-2 space-x-reverse mb-3">
                        <div class="w-6 h-6 bg-violet-500/30 rounded-xl flex items-center justify-center">
                            @if($chatRoom->is_private)
                                <svg class="w-3 h-3 text-violet-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                </svg>
                            @else
                                <svg class="w-3 h-3 text-violet-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            @endif
                        </div>
                        <label class="text-xs font-bold text-violet-300">نوع</label>
                    </div>
                    <p class="text-sm font-bold text-white">{{ $chatRoom->is_private ? 'خصوصی' : 'عمومی' }}</p>
                </div>

                <!-- Online Count -->
                <div class="bg-gradient-to-br from-green-500/20 to-emerald-500/20 backdrop-blur-xl rounded-3xl p-5 border border-white/10">
                    <div class="flex items-center space-x-2 space-x-reverse mb-3">
                        <div class="w-6 h-6 bg-green-500/30 rounded-xl flex items-center justify-center">
                            <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                        </div>
                        <label class="text-xs font-bold text-green-300">آنلاین</label>
                    </div>
                    <p class="text-lg font-bold text-white">{{ $onlineMembers->count() }} <span class="text-white/60 text-sm">نفر</span></p>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<style>
/* Telegram-like Styles */
.bg-telegram-bg {
    background-color: #f5f7fa;
    background-image:
        radial-gradient(circle at 25px 25px, rgba(59, 130, 246, 0.1) 2px, transparent 2px),
        radial-gradient(circle at 75px 75px, rgba(99, 102, 241, 0.1) 2px, transparent 2px);
    background-size: 100px 100px;
}

/* Custom Scrollbar - Telegram Style */
.telegram-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 130, 246, 0.3) transparent;
}

.telegram-scrollbar::-webkit-scrollbar {
    width: 4px;
}

.telegram-scrollbar::-webkit-scrollbar-track {
    background: transparent;
}

.telegram-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.3);
    border-radius: 2px;
}

.telegram-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.5);
}

/* Message Animations */
@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes messageSlideOut {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
}

.message-wrapper {
    animation: messageSlideIn 0.3s ease-out;
}

.message-wrapper.removing {
    animation: messageSlideOut 0.3s ease-in forwards;
}

/* Own Message Styles */
.own-message .bg-blue-500 {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    box-shadow: 0 1px 2px rgba(59, 130, 246, 0.3);
}

/* Other Message Styles */
.other-message .bg-white {
    background: #ffffff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Typing Animation */
@keyframes typingDot {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.4;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

.typing-dot {
    animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: 0s; }
.typing-dot:nth-child(2) { animation-delay: 0.2s; }
.typing-dot:nth-child(3) { animation-delay: 0.4s; }

/* Input Focus Effects */
.message-input-container:focus-within {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* Responsive Design */
@media (max-width: 640px) {
    .message-wrapper {
        margin-left: 0;
        margin-right: 0;
    }

    .max-w-xs {
        max-width: calc(100vw - 80px);
    }

    #emoji-picker {
        left: 8px;
        right: 8px;
    }
}

/* Message Hover Effects */
.message-wrapper:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Selection Styles */
.message-wrapper.selected {
    background-color: rgba(59, 130, 246, 0.1);
}

/* Message Actions Positioning */
.message-wrapper {
    position: relative;
}

.message-wrapper .group:hover .opacity-0 {
    opacity: 1;
}

/* Ensure action buttons don't interfere with layout */
.message-wrapper .absolute {
    pointer-events: none;
}

.message-wrapper .absolute button {
    pointer-events: auto;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    /* Hide action buttons on mobile to keep it clean */
    .message-wrapper .opacity-0 {
        display: none;
    }

    /* Add long press for mobile actions */
    .message-wrapper {
        -webkit-user-select: none;
        -moz-user-select: none;
        user-select: none;
    }

    /* Better message spacing on mobile */
    .message-wrapper {
        margin-left: 0;
        margin-right: 0;
    }

    /* Responsive message width */
    .max-w-xs {
        max-width: calc(100vw - 100px);
    }

    .max-w-md {
        max-width: calc(100vw - 80px);
    }

    .max-w-lg {
        max-width: calc(100vw - 60px);
    }
}
</style>

<script>
const chatRoomId = {{ $chatRoom->id }};
const currentUserId = {{ auth()->id() }};
const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

document.addEventListener('DOMContentLoaded', function() {
    initializeChat();
    setupTelegramFeatures();
});

function setupTelegramFeatures() {
    // Auto-resize textarea - Telegram style
    const messageInput = document.getElementById('message-input');
    const sendButton = document.getElementById('send-button');
    const charCounter = document.getElementById('char-counter');

    messageInput.addEventListener('input', function() {
        // Auto-resize
        this.style.height = 'auto';
        this.style.height = Math.min(this.scrollHeight, 128) + 'px';

        // Update character counter
        const charCount = document.getElementById('char-count');
        const length = this.value.length;
        charCount.textContent = length;

        // Show/hide character counter
        if (length > 1500) {
            charCounter.classList.remove('hidden');
            if (length > 1800) {
                charCount.className = 'text-red-500 font-bold';
            } else {
                charCount.className = 'text-yellow-500 font-medium';
            }
        } else {
            charCounter.classList.add('hidden');
        }

        // Update send button state
        if (this.value.trim()) {
            sendButton.classList.remove('opacity-50');
            sendButton.disabled = false;
        } else {
            sendButton.classList.add('opacity-50');
            sendButton.disabled = true;
        }
    });

    // Typing indicator with debounce
    let typingTimer;
    let isTyping = false;

    messageInput.addEventListener('input', function() {
        if (!isTyping && this.value.trim()) {
            showTypingIndicator();
            isTyping = true;
        }

        clearTimeout(typingTimer);
        typingTimer = setTimeout(() => {
            hideTypingIndicator();
            isTyping = false;
        }, 1000);
    });

    // Scroll detection for "scroll to bottom" button
    const messagesContainer = document.getElementById('messages-container');
    const scrollToBottomBtn = document.getElementById('scroll-to-bottom');

    messagesContainer.addEventListener('scroll', function() {
        const isAtBottom = this.scrollTop + this.clientHeight >= this.scrollHeight - 100;

        if (isAtBottom) {
            scrollToBottomBtn.classList.add('hidden');
        } else {
            scrollToBottomBtn.classList.remove('hidden');
        }
    });

    // Enhanced emoji picker
    window.toggleEmojiPicker = function() {
        const picker = document.getElementById('emoji-picker');
        const isHidden = picker.classList.contains('hidden');

        if (isHidden) {
            picker.classList.remove('hidden');
            picker.style.transform = 'translateY(10px)';
            picker.style.opacity = '0';

            setTimeout(() => {
                picker.style.transform = 'translateY(0)';
                picker.style.opacity = '1';
            }, 10);
        } else {
            picker.style.transform = 'translateY(10px)';
            picker.style.opacity = '0';

            setTimeout(() => {
                picker.classList.add('hidden');
            }, 200);
        }
    };

    window.insertEmoji = function(emoji) {
        const input = document.getElementById('message-input');
        const start = input.selectionStart;
        const end = input.selectionEnd;
        const text = input.value;

        input.value = text.substring(0, start) + emoji + text.substring(end);
        input.selectionStart = input.selectionEnd = start + emoji.length;
        input.focus();

        // Trigger input event
        input.dispatchEvent(new Event('input'));

        // Hide emoji picker
        document.getElementById('emoji-picker').classList.add('hidden');
    };

    // Smooth scroll to bottom
    window.scrollToBottom = function() {
        const container = document.getElementById('messages-container');
        container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth'
        });

        // Hide scroll button
        document.getElementById('scroll-to-bottom').classList.add('hidden');
    };

    // Mobile back button
    window.goBack = function() {
        window.history.back();
    };

    // More options menu
    window.toggleMoreOptions = function() {
        const menu = document.getElementById('more-options-menu');
        menu.classList.toggle('hidden');
    };

    // Message menu toggle
    window.toggleMessageMenu = function(messageId) {
        // Close all other menus first
        document.querySelectorAll('[id^="message-menu-"]').forEach(menu => {
            if (menu.id !== `message-menu-${messageId}`) {
                menu.classList.add('hidden');
            }
        });

        const menu = document.getElementById(`message-menu-${messageId}`);
        menu.classList.toggle('hidden');
    };

    // Long press for mobile
    let longPressTimer;
    let isLongPress = false;

    document.addEventListener('touchstart', function(e) {
        const messageWrapper = e.target.closest('.message-wrapper');
        if (messageWrapper && window.innerWidth <= 768) {
            isLongPress = false;
            longPressTimer = setTimeout(() => {
                isLongPress = true;
                const messageId = messageWrapper.dataset.messageId;
                const isOwn = messageWrapper.classList.contains('own-message');

                if (isOwn && messageId) {
                    // Show context menu for own messages
                    const menu = document.getElementById(`message-menu-${messageId}`);
                    if (menu) {
                        // Close all other menus
                        document.querySelectorAll('[id^="message-menu-"]').forEach(m => {
                            if (m.id !== `message-menu-${messageId}`) {
                                m.classList.add('hidden');
                            }
                        });
                        menu.classList.remove('hidden');

                        // Add vibration feedback if available
                        if (navigator.vibrate) {
                            navigator.vibrate(50);
                        }
                    }
                }
            }, 500); // 500ms long press
        }
    });

    document.addEventListener('touchend', function(e) {
        clearTimeout(longPressTimer);
    });

    document.addEventListener('touchmove', function(e) {
        clearTimeout(longPressTimer);
    });

    // Close menus when clicking outside
    document.addEventListener('click', function(e) {
        const moreMenu = document.getElementById('more-options-menu');
        const emojiPicker = document.getElementById('emoji-picker');

        if (!e.target.closest('#more-options-menu') && !e.target.closest('button[onclick="toggleMoreOptions()"]')) {
            moreMenu.classList.add('hidden');
        }

        if (!e.target.closest('#emoji-picker') && !e.target.closest('button[onclick="toggleEmojiPicker()"]')) {
            emojiPicker.classList.add('hidden');
        }

        // Close message menus when clicking outside
        if (!e.target.closest('[id^="message-menu-"]') && !e.target.closest('button[onclick^="toggleMessageMenu"]')) {
            document.querySelectorAll('[id^="message-menu-"]').forEach(menu => {
                menu.classList.add('hidden');
            });
        }
    });

    // Enhanced members sidebar
    window.toggleMembersList = function() {
        const sidebar = document.getElementById('members-sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        const isHidden = sidebar.classList.contains('hidden');

        if (isHidden) {
            // Show sidebar
            sidebar.classList.remove('hidden');
            overlay.classList.remove('hidden');

            // Animate in
            setTimeout(() => {
                sidebar.classList.remove('translate-x-full');
                overlay.classList.remove('opacity-0');
            }, 10);
        } else {
            // Hide sidebar
            sidebar.classList.add('translate-x-full');
            overlay.classList.add('opacity-0');

            setTimeout(() => {
                sidebar.classList.add('hidden');
                overlay.classList.add('hidden');
            }, 300);
        }
    };

    // Room actions
    window.clearChat = function() {
        if (confirm('آیا مطمئن هستید که می‌خواهید تاریخچه چت را پاک کنید؟')) {
            // Implementation for clearing chat
            console.log('Clear chat');
        }
        document.getElementById('more-options-menu').classList.add('hidden');
    };

    window.leaveRoom = function() {
        if (confirm('آیا مطمئن هستید که می‌خواهید از این روم خارج شوید؟')) {
            window.location.href = '/chat-rooms/{{ $chatRoom->id }}/leave';
        }
    };

    window.toggleRoomInfo = function() {
        const modal = document.getElementById('room-info-modal');
        const modalContent = document.getElementById('modal-content');

        if (modal.classList.contains('hidden')) {
            modal.classList.remove('hidden');
            setTimeout(() => {
                modalContent.classList.remove('scale-95', 'opacity-0');
                modalContent.classList.add('scale-100', 'opacity-100');
            }, 10);
        } else {
            modalContent.classList.remove('scale-100', 'opacity-100');
            modalContent.classList.add('scale-95', 'opacity-0');
            setTimeout(() => {
                modal.classList.add('hidden');
            }, 300);
        }

        document.getElementById('more-options-menu').classList.add('hidden');
    };

    window.toggleSearch = function() {
        // Implementation for search functionality
        console.log('Toggle search');
    };

    // Copy room code with enhanced feedback
    window.copyRoomCode = function() {
        const roomCode = '{{ $chatRoom->room_code }}';
        navigator.clipboard.writeText(roomCode).then(() => {
            showNotification('کد روم کپی شد!', 'success');
        }).catch(() => {
            showNotification('خطا در کپی کردن کد', 'error');
        });
    };
}

function showTypingIndicator() {
    const indicator = document.getElementById('typing-indicator');
    indicator.classList.remove('hidden');
    indicator.classList.add('animate-fade-in');
}

function hideTypingIndicator() {
    const indicator = document.getElementById('typing-indicator');
    indicator.classList.add('hidden');
    indicator.classList.remove('animate-fade-in');
}

// Enhanced message animations
function addMessageWithAnimation(messageElement) {
    messageElement.classList.add('animate-fade-in');
    setTimeout(() => {
        messageElement.classList.remove('animate-fade-in');
    }, 500);
}

// Sound effects (optional)
function playNotificationSound() {
    // You can add sound effects here
    // const audio = new Audio('/sounds/notification.mp3');
    // audio.play().catch(() => {});
}
</script>
<script src="{{ asset('js/chat.js') }}"></script>
@endpush
@endsection
