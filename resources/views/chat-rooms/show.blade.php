@extends('layouts.app')

@section('title', $chatRoom->name)

@section('content')
<div class="h-screen flex flex-col bg-gray-50">
    <!-- Chat Header -->
    <div class="bg-white border-b border-gray-200 px-4 py-3 sm:px-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <a href="{{ route('dashboard') }}"
                   class="text-gray-400 hover:text-gray-600 ml-4">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                <div>
                    <h1 class="text-lg font-medium text-gray-900">{{ $chatRoom->name }}</h1>
                    <p class="text-sm text-gray-500">
                        {{ $members->count() }} عضو •
                        <span id="online-count">{{ $onlineMembers->count() }}</span> آنلاین
                    </p>
                </div>
            </div>

            <div class="flex items-center space-x-2 space-x-reverse">
                <button type="button" onclick="toggleRoomInfo()"
                        class="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100">
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </button>

                <button type="button" onclick="toggleMembersList()"
                        class="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100">
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <div class="flex-1 flex overflow-hidden">
        <!-- Main Chat Area -->
        <div class="flex-1 flex flex-col">
            <!-- Messages Container -->
            <div id="messages-container" class="flex-1 overflow-y-auto p-4 space-y-4">
                @foreach($messages as $message)
                    <div class="message-item" data-message-id="{{ $message->id }}">
                        @include('partials.message', ['message' => $message])
                    </div>
                @endforeach
            </div>

            <!-- Message Input -->
            <div class="bg-white border-t border-gray-200 p-4">
                <div id="reply-preview" class="hidden mb-3 p-3 bg-gray-50 rounded-lg border-r-4 border-blue-500">
                    <div class="flex justify-between items-start">
                        <div>
                            <p class="text-sm text-gray-600">پاسخ به:</p>
                            <p id="reply-content" class="text-sm text-gray-800"></p>
                        </div>
                        <button onclick="cancelReply()" class="text-gray-400 hover:text-gray-600">
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <form id="message-form" class="flex space-x-2 space-x-reverse">
                    @csrf
                    <input type="hidden" id="reply-to" name="reply_to" value="">

                    <div class="flex-1">
                        <input type="text"
                               id="message-input"
                               name="content"
                               placeholder="پیام خود را بنویسید..."
                               class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                               maxlength="2000"
                               autocomplete="off">
                    </div>

                    <label for="file-input" class="cursor-pointer p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                        </svg>
                    </label>
                    <input type="file" id="file-input" class="hidden" accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt">

                    <button type="submit"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                    </button>
                </form>
            </div>
        </div>

        <!-- Sidebar (Members List) -->
        <div id="members-sidebar" class="hidden w-64 bg-white border-r border-gray-200 overflow-y-auto">
            <div class="p-4">
                <h3 class="text-lg font-medium text-gray-900 mb-4">اعضای روم</h3>
                <div class="space-y-2">
                    @foreach($members as $member)
                        <div class="flex items-center space-x-3 space-x-reverse p-2 rounded-lg hover:bg-gray-50">
                            <div class="flex-shrink-0">
                                <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center relative">
                                    <span class="text-sm font-medium text-gray-700">
                                        {{ substr($member->name, 0, 1) }}
                                    </span>
                                    @if($member->isOnline())
                                        <span class="absolute -bottom-0 -left-0 h-3 w-3 bg-green-400 border-2 border-white rounded-full"></span>
                                    @endif
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">
                                    {{ $member->name }}
                                    @if($member->pivot->role === 'admin')
                                        <span class="text-xs text-blue-600">(مدیر)</span>
                                    @elseif($member->pivot->role === 'moderator')
                                        <span class="text-xs text-green-600">(ناظر)</span>
                                    @endif
                                </p>
                                <p class="text-xs text-gray-500">
                                    @if($member->isOnline())
                                        آنلاین
                                    @else
                                        {{ $member->last_seen ? $member->last_seen->diffForHumans() : 'آفلاین' }}
                                    @endif
                                </p>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Room Info Modal -->
<div id="room-info-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">اطلاعات روم</h3>
                <button onclick="toggleRoomInfo()" class="text-gray-400 hover:text-gray-600">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="space-y-3">
                <div>
                    <label class="block text-sm font-medium text-gray-700">نام روم</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $chatRoom->name }}</p>
                </div>

                @if($chatRoom->description)
                <div>
                    <label class="block text-sm font-medium text-gray-700">توضیحات</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $chatRoom->description }}</p>
                </div>
                @endif

                <div>
                    <label class="block text-sm font-medium text-gray-700">کد روم</label>
                    <div class="mt-1 flex items-center space-x-2 space-x-reverse">
                        <code class="text-sm bg-gray-100 px-2 py-1 rounded">{{ $chatRoom->room_code }}</code>
                        <button onclick="copyRoomCode()" class="text-blue-600 hover:text-blue-800 text-sm">کپی</button>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700">سازنده</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $chatRoom->creator->name }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700">تعداد اعضا</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $members->count() }} از {{ $chatRoom->max_members }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700">نوع روم</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $chatRoom->is_private ? 'خصوصی' : 'عمومی' }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
const chatRoomId = {{ $chatRoom->id }};
const currentUserId = {{ auth()->id() }};
const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

document.addEventListener('DOMContentLoaded', function() {
    initializeChat();
});
</script>
<script src="{{ asset('js/chat.js') }}"></script>
@endpush
@endsection
