@extends('layouts.app')

@section('title', $chatRoom->name)

@section('content')
<!-- Ultra Modern Chat Interface -->
<div class="h-screen flex flex-col relative overflow-hidden">
    <!-- Animated Background -->
    <div class="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"></div>
    <div class="absolute inset-0 opacity-20">
        <div class="absolute top-0 -left-4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
        <div class="absolute top-0 -right-4 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
        <div class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
    </div>
    <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>

    <!-- Ultra Modern Header -->
    <div class="relative z-10 bg-black/20 backdrop-blur-xl border-b border-white/10">
        <div class="flex items-center px-4 py-4 sm:px-6">
            <!-- Back Button (Mobile) -->
            <button onclick="goBack()" class="lg:hidden p-2 -ml-2 mr-3 text-white/70 hover:text-white rounded-xl hover:bg-white/10 transition-all duration-200">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>

            <!-- Room Avatar & Info -->
            <div class="flex items-center flex-1 min-w-0">
                <div class="relative flex-shrink-0">
                    <div class="w-12 h-12 sm:w-14 sm:h-14 rounded-2xl bg-gradient-to-br from-cyan-400 via-blue-500 to-purple-600 flex items-center justify-center shadow-2xl ring-4 ring-white/20">
                        <span class="text-white font-bold text-lg sm:text-xl">
                            {{ substr($chatRoom->name, 0, 1) }}
                        </span>
                    </div>
                    <div class="absolute -bottom-1 -right-1 w-4 h-4 sm:w-5 sm:h-5 bg-gradient-to-r from-green-400 to-emerald-500 border-3 border-black/20 rounded-full animate-pulse shadow-lg"></div>
                    <!-- Glow Effect -->
                    <div class="absolute inset-0 rounded-2xl bg-gradient-to-br from-cyan-400 via-blue-500 to-purple-600 opacity-30 blur-lg animate-pulse"></div>
                </div>

                <div class="ml-4 flex-1 min-w-0">
                    <div class="flex items-center">
                        <h1 class="text-lg sm:text-xl font-bold text-white truncate bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                            {{ $chatRoom->name }}
                        </h1>
                        @if($chatRoom->is_private)
                            <div class="ml-2 p-1 bg-amber-500/20 rounded-lg">
                                <svg class="w-4 h-4 text-amber-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        @endif
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse text-sm text-white/70">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse"></div>
                            <span id="online-count">{{ $onlineMembers->count() }}</span>
                        </div>
                        <span>از</span>
                        <span>{{ $members->count() }}</span>
                        <span>عضو آنلاین</span>
                    </div>
                </div>
            </div>

            <!-- Header Actions -->
            <div class="flex items-center space-x-2 space-x-reverse">
                <!-- Search Button -->
                <button type="button" onclick="toggleSearch()"
                        class="group p-3 text-white/70 hover:text-white hover:bg-white/10 rounded-xl transition-all duration-200 hover:scale-105">
                    <svg class="w-5 h-5 group-hover:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </button>

                <!-- Members Button -->
                <button type="button" onclick="toggleMembersList()"
                        class="group p-3 text-white/70 hover:text-white hover:bg-white/10 rounded-xl transition-all duration-200 hover:scale-105">
                    <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </button>

                <!-- More Options -->
                <div class="relative">
                    <button type="button" onclick="toggleMoreOptions()"
                            class="group p-3 text-white/70 hover:text-white hover:bg-white/10 rounded-xl transition-all duration-200 hover:scale-105">
                        <svg class="w-5 h-5 group-hover:rotate-90 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                        </svg>
                    </button>

                    <!-- Dropdown Menu -->
                    <div id="more-options-menu" class="hidden absolute right-0 top-full mt-2 w-52 bg-black/80 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/10 py-2 z-50">
                        <button onclick="toggleRoomInfo()" class="w-full text-right px-4 py-3 text-sm text-white/90 hover:bg-white/10 flex items-center transition-colors rounded-xl mx-2">
                            <svg class="w-4 h-4 ml-3 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            اطلاعات روم
                        </button>
                        <button onclick="clearChat()" class="w-full text-right px-4 py-3 text-sm text-white/90 hover:bg-white/10 flex items-center transition-colors rounded-xl mx-2">
                            <svg class="w-4 h-4 ml-3 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            پاک کردن چت
                        </button>
                        @if($chatRoom->created_by !== auth()->id())
                        <button onclick="leaveRoom()" class="w-full text-right px-4 py-3 text-sm text-red-400 hover:bg-red-500/10 flex items-center transition-colors rounded-xl mx-2">
                            <svg class="w-4 h-4 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                            </svg>
                            خروج از روم
                        </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Chat Container -->
    <div class="flex-1 flex overflow-hidden relative">
        <!-- Ultra Modern Messages Area -->
        <div class="flex-1 flex flex-col relative">
            <!-- Messages Container -->
            <div id="messages-container" class="flex-1 overflow-y-auto px-4 py-6 space-y-2 scroll-smooth modern-scrollbar">
                <!-- Date Separator -->
                <div class="flex justify-center my-6">
                    <div class="bg-white/10 backdrop-blur-xl rounded-full px-4 py-2 border border-white/20">
                        <span class="text-xs text-white/80 font-medium">
                            {{ now()->format('d F Y') }}
                        </span>
                    </div>
                </div>

                <!-- Welcome Message -->
                <div class="flex justify-center my-8">
                    <div class="bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-xl rounded-3xl px-6 py-4 max-w-sm text-center border border-white/20 shadow-2xl">
                        <div class="text-4xl mb-3 animate-bounce">🚀</div>
                        <p class="text-white font-semibold text-base mb-2">
                            به <span class="bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent font-bold">{{ $chatRoom->name }}</span> خوش آمدید!
                        </p>
                        <p class="text-white/70 text-sm">
                            شروع کنید و با دوستانتان چت کنید ✨
                        </p>
                    </div>
                </div>

                @foreach($messages as $message)
                    <div class="message-item" data-message-id="{{ $message->id }}">
                        @include('partials.ultra-message', ['message' => $message])
                    </div>
                @endforeach

                <!-- Typing Indicator - Ultra Modern -->
                <div id="typing-indicator" class="hidden">
                    <div class="flex items-start space-x-3 space-x-reverse mb-3">
                        <div class="w-10 h-10 rounded-2xl bg-gradient-to-br from-gray-400 to-gray-600 flex items-center justify-center flex-shrink-0 shadow-lg">
                            <span class="text-sm font-medium text-white">?</span>
                        </div>
                        <div class="bg-white/90 backdrop-blur-xl rounded-3xl rounded-bl-lg px-5 py-3 shadow-xl border border-white/20 max-w-xs">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <div class="flex space-x-1 space-x-reverse">
                                    <div class="w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-bounce"></div>
                                    <div class="w-2 h-2 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                                    <div class="w-2 h-2 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                                </div>
                                <span class="text-sm text-gray-600 mr-2 font-medium">در حال تایپ</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Scroll to Bottom Button -->
                <div id="scroll-to-bottom" class="hidden fixed bottom-28 right-6 z-20">
                    <button onclick="scrollToBottom()" class="w-14 h-14 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded-2xl shadow-2xl flex items-center justify-center transition-all duration-300 hover:scale-110 hover:rotate-12 border border-white/20">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Ultra Modern Message Input -->
            <div class="bg-black/20 backdrop-blur-xl border-t border-white/10 p-6 relative z-10">
                <!-- Reply Preview - Ultra Modern -->
                <div id="reply-preview" class="hidden mb-4 bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-xl border-l-4 border-purple-400 rounded-r-2xl p-4 mx-2 border border-white/10">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <div class="text-xs font-semibold text-purple-300 mb-1 flex items-center">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                </svg>
                                پاسخ به:
                            </div>
                            <div id="reply-content" class="text-sm text-white/90 line-clamp-2"></div>
                        </div>
                        <button onclick="cancelReply()" class="p-2 text-white/60 hover:text-white hover:bg-white/10 rounded-xl transition-all duration-200">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Emoji Picker - Ultra Modern -->
                <div id="emoji-picker" class="hidden absolute bottom-full left-4 right-4 mb-4 bg-black/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-6 z-50">
                    <div class="grid grid-cols-8 sm:grid-cols-10 gap-3 max-h-64 overflow-y-auto modern-scrollbar">
                        @php
                            $emojis = ['😀','😃','😄','😁','😆','😅','😂','🤣','😊','😇','🙂','🙃','😉','😌','😍','🥰','😘','😗','😙','😚','😋','😛','😝','😜','🤪','🤨','🧐','🤓','😎','🤩','🥳','😏','😒','😞','😔','😟','😕','🙁','☹️','😣','😖','😫','😩','🥺','😢','😭','😤','😠','😡','🤬','🤯','😳','🥵','🥶','😱','😨','😰','😥','😓','🤗','🤔','🤭','🤫','🤥','😶','😐','😑','😬','🙄','😯','😦','😧','😮','😲','🥱','😴','🤤','😪','😵','🤐','🥴','🤢','🤮','🤧','😷','🤒','🤕','🤑','🤠','😈','👿','👹','👺','🤡','💩','👻','💀','☠️','👽','👾','🤖','🎃','😺','😸','😹','😻','😼','😽','🙀','😿','😾','❤️','🧡','💛','💚','💙','💜','🖤','🤍','🤎','💔','❣️','💕','💞','💓','💗','💖','💘','💝','💟','☮️','✝️','☪️','🕉️','☸️','✡️','🔯','🕎','☯️','☦️','🛐','⛎','♈','♉','♊','♋','♌','♍','♎','♏','♐','♑','♒','♓','🆔','⚛️','🉑','☢️','☣️','📴','📳','🈶','🈚','🈸','🈺','🈷️','✴️','🆚','💮','🉐','㊙️','㊗️','🈴','🈵','🈹','🈲','🅰️','🅱️','🆎','🆑','🅾️','🆘','❌','⭕','🛑','⛔','📛','🚫','💯','💢','♨️','🚷','🚯','🚳','🚱','🔞','📵','🚭','❗','❕','❓','❔','‼️','⁉️','🔅','🔆','〽️','⚠️','🚸','🔱','⚜️','🔰','♻️','✅','🈯','💹','❇️','✳️','❎','🌐','💠','Ⓜ️','🌀','💤','🏧','🚾','♿','🅿️','🈳','🈂️','🛂','🛃','🛄','🛅','🚹','🚺','🚼','🚻','🚮','🎦','📶','🈁','🔣','ℹ️','🔤','🔡','🔠','🆖','🆗','🆙','🆒','🆕','🆓','0️⃣','1️⃣','2️⃣','3️⃣','4️⃣','5️⃣','6️⃣','7️⃣','8️⃣','9️⃣','🔟','🔢','#️⃣','*️⃣','⏏️','▶️','⏸️','⏯️','⏹️','⏺️','⏭️','⏮️','⏩','⏪','⏫','⏬','◀️','🔼','🔽','➡️','⬅️','⬆️','⬇️','↗️','↘️','↙️','↖️','↕️','↔️','↪️','↩️','⤴️','⤵️','🔀','🔁','🔂','🔄','🔃','🎵','🎶','➕','➖','➗','✖️','♾️','💲','💱','™️','©️','®️','〰️','➰','➿','🔚','🔙','🔛','🔝','🔜','✔️','☑️','🔘','⚪','⚫','🔴','🔵','🔺','🔻','🔸','🔹','🔶','🔷','🔳','🔲','▪️','▫️','◾','◽','◼️','◻️','⬛','⬜','🔈','🔇','🔉','🔊','🔔','🔕','📣','📢','👁️‍🗨️','💬','💭','🗯️','♠️','♣️','♥️','♦️','🃏','🎴','🀄','🕐','🕑','🕒','🕓','🕔','🕕','🕖','🕗','🕘','🕙','🕚','🕛','🕜','🕝','🕞','🕟','🕠','🕡','🕢','🕣','🕤','🕥','🕦','🕧'];
                        @endphp
                        @foreach($emojis as $emoji)
                            <button onclick="insertEmoji('{{ $emoji }}')" class="p-3 hover:bg-white/10 rounded-xl transition-all duration-200 text-xl hover:scale-110">{{ $emoji }}</button>
                        @endforeach
                    </div>
                </div>

                <!-- Message Input Form -->
                <form id="message-form" class="flex items-end space-x-3 space-x-reverse">
                    @csrf
                    <input type="hidden" id="reply-to" name="reply_to" value="">

                    <!-- Attachment Button -->
                    <div class="flex-shrink-0">
                        <label for="file-input" class="group cursor-pointer p-3 text-white/60 hover:text-white hover:bg-white/10 rounded-2xl transition-all duration-200 hover:scale-105">
                            <svg class="w-6 h-6 group-hover:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                            </svg>
                        </label>
                        <input type="file" id="file-input" class="hidden" accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt">
                    </div>

                    <!-- Message Input Container -->
                    <div class="flex-1 relative bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 focus-within:border-purple-400 focus-within:bg-white/15 transition-all duration-200 message-input-container">
                        <div class="flex items-end">
                            <!-- Emoji Button -->
                            <button type="button" onclick="toggleEmojiPicker()" class="group p-4 text-white/60 hover:text-yellow-400 transition-all duration-200">
                                <svg class="w-5 h-5 group-hover:scale-110 group-hover:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </button>

                            <!-- Text Input -->
                            <textarea id="message-input"
                                   name="content"
                                   placeholder="پیام خود را بنویسید..."
                                   class="flex-1 resize-none border-0 bg-transparent px-2 py-4 text-white placeholder-white/50 focus:outline-none max-h-32 text-sm"
                                   rows="1"
                                   maxlength="2000"
                                   autocomplete="off"></textarea>

                            <!-- Send Button -->
                            <div class="p-2">
                                <button type="submit" id="send-button"
                                        class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded-2xl flex items-center justify-center transition-all duration-200 hover:scale-105 hover:rotate-12 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Character Counter -->
                        <div class="absolute bottom-2 left-4 text-xs text-white/40 hidden" id="char-counter">
                            <span id="char-count">0</span>/2000
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Ultra Modern Members Sidebar -->
        <div id="members-sidebar" class="hidden fixed inset-y-0 right-0 z-50 w-80 bg-black/40 backdrop-blur-2xl shadow-2xl transform transition-all duration-500 ease-out translate-x-full lg:relative lg:translate-x-0 lg:shadow-none border-l border-white/10">
            <!-- Animated Background -->
            <div class="absolute inset-0 overflow-hidden">
                <div class="absolute top-0 -right-4 w-72 h-72 bg-purple-300/10 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
                <div class="absolute bottom-0 -left-4 w-72 h-72 bg-pink-300/10 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
            </div>

            <!-- Sidebar Header -->
            <div class="relative z-10 flex items-center justify-between p-6 border-b border-white/10 bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-xl">
                <div class="flex items-center space-x-3 space-x-reverse">
                    <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-bold text-white">اعضای روم</h3>
                </div>
                <div class="flex items-center space-x-3 space-x-reverse">
                    <span class="bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                        {{ $members->count() }}
                    </span>
                    <button onclick="toggleMembersList()" class="lg:hidden p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-xl transition-all duration-200">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Members List -->
            <div class="relative z-10 flex-1 overflow-y-auto modern-scrollbar">
                <!-- Online Members Section -->
                @if($onlineMembers->count() > 0)
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full mr-3 animate-pulse shadow-lg"></div>
                        <span class="text-sm font-bold text-green-300">آنلاین — {{ $onlineMembers->count() }}</span>
                    </div>

                    <div class="space-y-2">
                        @foreach($members->where('is_online', true) as $member)
                            <div class="group flex items-center p-4 rounded-2xl hover:bg-white/10 transition-all duration-300 cursor-pointer hover:scale-105">
                                <div class="relative flex-shrink-0">
                                    <div class="w-12 h-12 rounded-2xl bg-gradient-to-br from-cyan-400 via-blue-500 to-purple-600 flex items-center justify-center text-white font-bold shadow-xl ring-2 ring-white/20">
                                        {{ substr($member->name, 0, 1) }}
                                    </div>
                                    <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-gradient-to-r from-green-400 to-emerald-500 border-2 border-black/20 rounded-full animate-pulse shadow-lg"></div>
                                    <!-- Glow Effect -->
                                    <div class="absolute inset-0 rounded-2xl bg-gradient-to-br from-cyan-400 via-blue-500 to-purple-600 opacity-30 blur-lg group-hover:opacity-50 transition-opacity duration-300"></div>
                                </div>

                                <div class="mr-4 flex-1 min-w-0">
                                    <div class="flex items-center justify-between">
                                        <p class="text-sm font-bold text-white truncate group-hover:text-cyan-300 transition-colors duration-300">
                                            {{ $member->name }}
                                        </p>
                                        @if($member->pivot->role === 'admin')
                                            <div class="flex items-center space-x-1 space-x-reverse bg-blue-500/20 px-2 py-1 rounded-lg">
                                                <span class="text-yellow-400 text-sm">👑</span>
                                                <span class="text-xs text-blue-300 font-medium">مدیر</span>
                                            </div>
                                        @elseif($member->pivot->role === 'moderator')
                                            <div class="flex items-center space-x-1 space-x-reverse bg-green-500/20 px-2 py-1 rounded-lg">
                                                <span class="text-green-400 text-sm">🛡️</span>
                                                <span class="text-xs text-green-300 font-medium">ناظر</span>
                                            </div>
                                        @endif
                                    </div>
                                    <p class="text-xs text-green-400 font-medium flex items-center">
                                        <div class="w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse"></div>
                                        آنلاین
                                    </p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Offline Members Section -->
                @if($members->where('is_online', false)->count() > 0)
                <div class="p-6 {{ $onlineMembers->count() > 0 ? 'border-t border-white/10' : '' }}">
                    <div class="flex items-center mb-4">
                        <div class="w-3 h-3 bg-gray-500 rounded-full mr-3 shadow-lg"></div>
                        <span class="text-sm font-bold text-gray-400">آفلاین — {{ $members->where('is_online', false)->count() }}</span>
                    </div>

                    <div class="space-y-2">
                        @foreach($members->where('is_online', false) as $member)
                            <div class="group flex items-center p-4 rounded-2xl hover:bg-white/5 transition-all duration-300 cursor-pointer">
                                <div class="relative flex-shrink-0">
                                    <div class="w-12 h-12 rounded-2xl bg-gradient-to-br from-gray-500 to-gray-700 flex items-center justify-center text-white font-bold shadow-lg opacity-60">
                                        {{ substr($member->name, 0, 1) }}
                                    </div>
                                </div>

                                <div class="mr-4 flex-1 min-w-0">
                                    <div class="flex items-center justify-between">
                                        <p class="text-sm font-medium text-white/70 truncate group-hover:text-white/90 transition-colors duration-300">
                                            {{ $member->name }}
                                        </p>
                                        @if($member->pivot->role === 'admin')
                                            <div class="flex items-center space-x-1 space-x-reverse bg-blue-500/10 px-2 py-1 rounded-lg">
                                                <span class="text-yellow-400/60 text-sm">👑</span>
                                                <span class="text-xs text-blue-300/60 font-medium">مدیر</span>
                                            </div>
                                        @elseif($member->pivot->role === 'moderator')
                                            <div class="flex items-center space-x-1 space-x-reverse bg-green-500/10 px-2 py-1 rounded-lg">
                                                <span class="text-green-400/60 text-sm">🛡️</span>
                                                <span class="text-xs text-green-300/60 font-medium">ناظر</span>
                                            </div>
                                        @endif
                                    </div>
                                    <p class="text-xs text-gray-500 flex items-center">
                                        <div class="w-2 h-2 bg-gray-500 rounded-full mr-1"></div>
                                        {{ $member->last_seen ? 'آخرین بازدید ' . $member->last_seen->diffForHumans() : 'آفلاین' }}
                                    </p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Sidebar Overlay (Mobile) -->
        <div id="sidebar-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden" onclick="toggleMembersList()"></div>
    </div>
</div>

<!-- Ultra Modern Room Info Modal -->
<div id="room-info-modal" class="hidden fixed inset-0 bg-black/60 backdrop-blur-xl overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
    <div class="relative bg-black/40 backdrop-blur-2xl rounded-3xl shadow-2xl border border-white/20 w-full max-w-lg transform transition-all duration-500 scale-95 opacity-0" id="modal-content">
        <!-- Animated Background -->
        <div class="absolute inset-0 rounded-3xl overflow-hidden">
            <div class="absolute top-0 -left-4 w-72 h-72 bg-purple-300/20 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
            <div class="absolute top-0 -right-4 w-72 h-72 bg-yellow-300/20 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
            <div class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300/20 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
        </div>

        <!-- Header -->
        <div class="relative z-10 bg-gradient-to-r from-purple-600/80 to-pink-600/80 backdrop-blur-xl rounded-t-3xl p-6 text-white border-b border-white/10">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 space-x-reverse">
                    <div class="h-16 w-16 rounded-3xl bg-gradient-to-br from-cyan-400 via-blue-500 to-purple-600 flex items-center justify-center shadow-2xl ring-4 ring-white/20">
                        <span class="text-2xl font-bold">{{ substr($chatRoom->name, 0, 1) }}</span>
                    </div>
                    <div>
                        <h3 class="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">اطلاعات روم</h3>
                        <p class="text-white/70 text-sm">جزئیات کامل چت روم</p>
                    </div>
                </div>
                <button onclick="toggleRoomInfo()" class="group p-3 hover:bg-white/20 rounded-2xl transition-all duration-200 hover:scale-105">
                    <svg class="h-6 w-6 group-hover:rotate-180 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Content -->
        <div class="relative z-10 p-6 space-y-6">
            <!-- Room Name -->
            <div class="bg-gradient-to-r from-blue-500/20 to-indigo-500/20 backdrop-blur-xl rounded-3xl p-6 border border-white/10">
                <div class="flex items-center space-x-3 space-x-reverse mb-3">
                    <div class="w-8 h-8 bg-blue-500/30 rounded-2xl flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                        </svg>
                    </div>
                    <label class="text-sm font-bold text-blue-300">نام روم</label>
                </div>
                <p class="text-xl font-bold text-white flex items-center">
                    {{ $chatRoom->name }}
                    @if($chatRoom->is_private)
                        <div class="mr-3 p-2 bg-amber-500/20 rounded-xl">
                            <svg class="h-5 w-5 text-amber-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    @endif
                </p>
            </div>

            @if($chatRoom->description)
            <!-- Description -->
            <div class="bg-gradient-to-r from-green-500/20 to-emerald-500/20 backdrop-blur-xl rounded-3xl p-6 border border-white/10">
                <div class="flex items-center space-x-3 space-x-reverse mb-3">
                    <div class="w-8 h-8 bg-green-500/30 rounded-2xl flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"></path>
                        </svg>
                    </div>
                    <label class="text-sm font-bold text-green-300">توضیحات</label>
                </div>
                <p class="text-white/90">{{ $chatRoom->description }}</p>
            </div>
            @endif

            <!-- Room Code -->
            <div class="bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-xl rounded-3xl p-6 border border-white/10">
                <div class="flex items-center space-x-3 space-x-reverse mb-4">
                    <div class="w-8 h-8 bg-purple-500/30 rounded-2xl flex items-center justify-center">
                        <svg class="w-4 h-4 text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                    </div>
                    <label class="text-sm font-bold text-purple-300">کد روم</label>
                </div>
                <div class="flex items-center justify-between bg-white/10 backdrop-blur-xl rounded-2xl p-4 border border-white/20">
                    <code class="text-2xl font-mono font-bold text-white tracking-wider">{{ $chatRoom->room_code }}</code>
                    <button onclick="copyRoomCode()" class="group flex items-center space-x-3 space-x-reverse bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-4 py-3 rounded-2xl transition-all duration-200 hover:scale-105 shadow-lg">
                        <svg class="h-5 w-5 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        <span class="font-bold">کپی</span>
                    </button>
                </div>
            </div>

            <!-- Stats Grid -->
            <div class="grid grid-cols-2 gap-4">
                <!-- Creator -->
                <div class="bg-gradient-to-br from-orange-500/20 to-red-500/20 backdrop-blur-xl rounded-3xl p-5 border border-white/10">
                    <div class="flex items-center space-x-2 space-x-reverse mb-3">
                        <div class="w-6 h-6 bg-orange-500/30 rounded-xl flex items-center justify-center">
                            <svg class="w-3 h-3 text-orange-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <label class="text-xs font-bold text-orange-300">سازنده</label>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <div class="h-8 w-8 rounded-2xl bg-gradient-to-br from-orange-400 to-red-500 flex items-center justify-center shadow-lg">
                            <span class="text-sm font-bold text-white">
                                {{ substr($chatRoom->creator->name, 0, 1) }}
                            </span>
                        </div>
                        <p class="text-sm font-bold text-white truncate">{{ $chatRoom->creator->name }}</p>
                    </div>
                </div>

                <!-- Member Count -->
                <div class="bg-gradient-to-br from-teal-500/20 to-cyan-500/20 backdrop-blur-xl rounded-3xl p-5 border border-white/10">
                    <div class="flex items-center space-x-2 space-x-reverse mb-3">
                        <div class="w-6 h-6 bg-teal-500/30 rounded-xl flex items-center justify-center">
                            <svg class="w-3 h-3 text-teal-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <label class="text-xs font-bold text-teal-300">اعضا</label>
                    </div>
                    <p class="text-lg font-bold text-white">{{ $members->count() }}<span class="text-white/60">/{{ $chatRoom->max_members }}</span></p>
                </div>

                <!-- Room Type -->
                <div class="bg-gradient-to-br from-violet-500/20 to-purple-500/20 backdrop-blur-xl rounded-3xl p-5 border border-white/10">
                    <div class="flex items-center space-x-2 space-x-reverse mb-3">
                        <div class="w-6 h-6 bg-violet-500/30 rounded-xl flex items-center justify-center">
                            @if($chatRoom->is_private)
                                <svg class="w-3 h-3 text-violet-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                </svg>
                            @else
                                <svg class="w-3 h-3 text-violet-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            @endif
                        </div>
                        <label class="text-xs font-bold text-violet-300">نوع</label>
                    </div>
                    <p class="text-sm font-bold text-white">{{ $chatRoom->is_private ? 'خصوصی' : 'عمومی' }}</p>
                </div>

                <!-- Online Count -->
                <div class="bg-gradient-to-br from-green-500/20 to-emerald-500/20 backdrop-blur-xl rounded-3xl p-5 border border-white/10">
                    <div class="flex items-center space-x-2 space-x-reverse mb-3">
                        <div class="w-6 h-6 bg-green-500/30 rounded-xl flex items-center justify-center">
                            <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                        </div>
                        <label class="text-xs font-bold text-green-300">آنلاین</label>
                    </div>
                    <p class="text-lg font-bold text-white">{{ $onlineMembers->count() }} <span class="text-white/60 text-sm">نفر</span></p>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<style>
/* Ultra Modern Styles */

/* Animated Blob Background */
@keyframes blob {
    0% {
        transform: translate(0px, 0px) scale(1);
    }
    33% {
        transform: translate(30px, -50px) scale(1.1);
    }
    66% {
        transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
        transform: translate(0px, 0px) scale(1);
    }
}

.animate-blob {
    animation: blob 7s infinite;
}

.animation-delay-2000 {
    animation-delay: 2s;
}

.animation-delay-4000 {
    animation-delay: 4s;
}

/* Modern Scrollbar */
.modern-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(168, 85, 247, 0.4) transparent;
}

.modern-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.modern-scrollbar::-webkit-scrollbar-track {
    background: transparent;
}

.modern-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, rgba(168, 85, 247, 0.4), rgba(236, 72, 153, 0.4));
    border-radius: 3px;
}

.modern-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, rgba(168, 85, 247, 0.6), rgba(236, 72, 153, 0.6));
}

/* Ultra Modern Message Animations */
@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.9) rotateX(10deg);
        filter: blur(5px);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1) rotateX(0deg);
        filter: blur(0px);
    }
}

@keyframes messageSlideOut {
    from {
        opacity: 1;
        transform: translateY(0) scale(1) rotateX(0deg);
        filter: blur(0px);
    }
    to {
        opacity: 0;
        transform: translateY(-30px) scale(0.9) rotateX(-10deg);
        filter: blur(5px);
    }
}

@keyframes messageGlow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
    }
    50% {
        box-shadow: 0 0 40px rgba(168, 85, 247, 0.6), 0 0 60px rgba(236, 72, 153, 0.4);
    }
}

.message-wrapper {
    animation: messageSlideIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.message-wrapper.removing {
    animation: messageSlideOut 0.4s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards;
}

.message-wrapper:hover {
    transform: translateY(-2px);
    transition: transform 0.3s ease;
}

/* Ultra Modern Message Styles */
.own-message .bg-gradient-to-br {
    background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 50%, #f59e0b 100%);
    box-shadow:
        0 10px 25px rgba(139, 92, 246, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.own-message .bg-gradient-to-br::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.own-message .bg-gradient-to-br:hover::before {
    left: 100%;
}

.other-message .bg-white\\/90 {
    background: rgba(255, 255, 255, 0.95);
    box-shadow:
        0 10px 25px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
}

/* Ultra Modern Typing Animation */
@keyframes typingDot {
    0%, 60%, 100% {
        transform: translateY(0) scale(1);
        opacity: 0.4;
    }
    30% {
        transform: translateY(-15px) scale(1.2);
        opacity: 1;
    }
}

@keyframes typingGlow {
    0%, 100% {
        box-shadow: 0 0 10px rgba(168, 85, 247, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(168, 85, 247, 0.6);
    }
}

.typing-dot {
    animation: typingDot 1.4s infinite ease-in-out, typingGlow 2s infinite ease-in-out;
    border-radius: 50%;
}

.typing-dot:nth-child(1) { animation-delay: 0s; }
.typing-dot:nth-child(2) { animation-delay: 0.2s; }
.typing-dot:nth-child(3) { animation-delay: 0.4s; }

/* Button Hover Effects */
@keyframes buttonPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(168, 85, 247, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(168, 85, 247, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(168, 85, 247, 0);
    }
}

.btn-pulse:hover {
    animation: buttonPulse 1s infinite;
}

/* Floating Animation */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.float {
    animation: float 3s ease-in-out infinite;
}

/* Glow Effect */
@keyframes glow {
    0%, 100% {
        filter: drop-shadow(0 0 5px rgba(168, 85, 247, 0.5));
    }
    50% {
        filter: drop-shadow(0 0 20px rgba(168, 85, 247, 0.8)) drop-shadow(0 0 30px rgba(236, 72, 153, 0.6));
    }
}

.glow {
    animation: glow 2s ease-in-out infinite;
}

/* Input Focus Effects */
.message-input-container:focus-within {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* Responsive Design */
@media (max-width: 640px) {
    .message-wrapper {
        margin-left: 0;
        margin-right: 0;
    }

    .max-w-xs {
        max-width: calc(100vw - 80px);
    }

    #emoji-picker {
        left: 8px;
        right: 8px;
    }
}

/* Hover Effects */
.message-wrapper:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Selection Styles */
.message-wrapper.selected {
    background-color: rgba(59, 130, 246, 0.1);
}

/* Loading Animation */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.loading {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Smooth Transitions */
* {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

/* Button Ripple Effect */
@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
    width: 300px;
    height: 300px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .bg-telegram-pattern {
        background-color: #1a1a1a;
    }

    .other-message .bg-white {
        background: #2d2d2d;
        color: #ffffff;
    }
}

/* Print Styles */
@media print {
    .message-wrapper {
        break-inside: avoid;
    }

    .telegram-scrollbar {
        overflow: visible !important;
    }
}
</style>

<script>
const chatRoomId = {{ $chatRoom->id }};
const currentUserId = {{ auth()->id() }};
const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

document.addEventListener('DOMContentLoaded', function() {
    initializeChat();
    setupTelegramFeatures();
});

function setupTelegramFeatures() {
    // Auto-resize textarea - Telegram style
    const messageInput = document.getElementById('message-input');
    const sendButton = document.getElementById('send-button');
    const charCounter = document.getElementById('char-counter');

    messageInput.addEventListener('input', function() {
        // Auto-resize
        this.style.height = 'auto';
        this.style.height = Math.min(this.scrollHeight, 128) + 'px';

        // Update character counter
        const charCount = document.getElementById('char-count');
        const length = this.value.length;
        charCount.textContent = length;

        // Show/hide character counter
        if (length > 1500) {
            charCounter.classList.remove('hidden');
            if (length > 1800) {
                charCount.className = 'text-red-500 font-bold';
            } else {
                charCount.className = 'text-yellow-500 font-medium';
            }
        } else {
            charCounter.classList.add('hidden');
        }

        // Update send button state
        if (this.value.trim()) {
            sendButton.classList.remove('opacity-50');
            sendButton.disabled = false;
        } else {
            sendButton.classList.add('opacity-50');
            sendButton.disabled = true;
        }
    });

    // Typing indicator with debounce
    let typingTimer;
    let isTyping = false;

    messageInput.addEventListener('input', function() {
        if (!isTyping && this.value.trim()) {
            showTypingIndicator();
            isTyping = true;
        }

        clearTimeout(typingTimer);
        typingTimer = setTimeout(() => {
            hideTypingIndicator();
            isTyping = false;
        }, 1000);
    });

    // Scroll detection for "scroll to bottom" button
    const messagesContainer = document.getElementById('messages-container');
    const scrollToBottomBtn = document.getElementById('scroll-to-bottom');

    messagesContainer.addEventListener('scroll', function() {
        const isAtBottom = this.scrollTop + this.clientHeight >= this.scrollHeight - 100;

        if (isAtBottom) {
            scrollToBottomBtn.classList.add('hidden');
        } else {
            scrollToBottomBtn.classList.remove('hidden');
        }
    });

    // Enhanced emoji picker
    window.toggleEmojiPicker = function() {
        const picker = document.getElementById('emoji-picker');
        const isHidden = picker.classList.contains('hidden');

        if (isHidden) {
            picker.classList.remove('hidden');
            picker.style.transform = 'translateY(10px)';
            picker.style.opacity = '0';

            setTimeout(() => {
                picker.style.transform = 'translateY(0)';
                picker.style.opacity = '1';
            }, 10);
        } else {
            picker.style.transform = 'translateY(10px)';
            picker.style.opacity = '0';

            setTimeout(() => {
                picker.classList.add('hidden');
            }, 200);
        }
    };

    window.insertEmoji = function(emoji) {
        const input = document.getElementById('message-input');
        const start = input.selectionStart;
        const end = input.selectionEnd;
        const text = input.value;

        input.value = text.substring(0, start) + emoji + text.substring(end);
        input.selectionStart = input.selectionEnd = start + emoji.length;
        input.focus();

        // Trigger input event
        input.dispatchEvent(new Event('input'));

        // Hide emoji picker
        document.getElementById('emoji-picker').classList.add('hidden');
    };

    // Smooth scroll to bottom
    window.scrollToBottom = function() {
        const container = document.getElementById('messages-container');
        container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth'
        });

        // Hide scroll button
        document.getElementById('scroll-to-bottom').classList.add('hidden');
    };

    // Mobile back button
    window.goBack = function() {
        window.history.back();
    };

    // More options menu
    window.toggleMoreOptions = function() {
        const menu = document.getElementById('more-options-menu');
        menu.classList.toggle('hidden');
    };

    // Close menus when clicking outside
    document.addEventListener('click', function(e) {
        const moreMenu = document.getElementById('more-options-menu');
        const emojiPicker = document.getElementById('emoji-picker');

        if (!e.target.closest('#more-options-menu') && !e.target.closest('button[onclick="toggleMoreOptions()"]')) {
            moreMenu.classList.add('hidden');
        }

        if (!e.target.closest('#emoji-picker') && !e.target.closest('button[onclick="toggleEmojiPicker()"]')) {
            emojiPicker.classList.add('hidden');
        }
    });

    // Enhanced members sidebar
    window.toggleMembersList = function() {
        const sidebar = document.getElementById('members-sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        const isHidden = sidebar.classList.contains('hidden');

        if (isHidden) {
            // Show sidebar
            sidebar.classList.remove('hidden');
            overlay.classList.remove('hidden');

            // Animate in
            setTimeout(() => {
                sidebar.classList.remove('translate-x-full');
                overlay.classList.remove('opacity-0');
            }, 10);
        } else {
            // Hide sidebar
            sidebar.classList.add('translate-x-full');
            overlay.classList.add('opacity-0');

            setTimeout(() => {
                sidebar.classList.add('hidden');
                overlay.classList.add('hidden');
            }, 300);
        }
    };

    // Room actions
    window.clearChat = function() {
        if (confirm('آیا مطمئن هستید که می‌خواهید تاریخچه چت را پاک کنید؟')) {
            // Implementation for clearing chat
            console.log('Clear chat');
        }
        document.getElementById('more-options-menu').classList.add('hidden');
    };

    window.leaveRoom = function() {
        if (confirm('آیا مطمئن هستید که می‌خواهید از این روم خارج شوید؟')) {
            window.location.href = '/chat-rooms/{{ $chatRoom->id }}/leave';
        }
    };

    window.toggleRoomInfo = function() {
        const modal = document.getElementById('room-info-modal');
        const modalContent = document.getElementById('modal-content');

        if (modal.classList.contains('hidden')) {
            modal.classList.remove('hidden');
            setTimeout(() => {
                modalContent.classList.remove('scale-95', 'opacity-0');
                modalContent.classList.add('scale-100', 'opacity-100');
            }, 10);
        } else {
            modalContent.classList.remove('scale-100', 'opacity-100');
            modalContent.classList.add('scale-95', 'opacity-0');
            setTimeout(() => {
                modal.classList.add('hidden');
            }, 300);
        }

        document.getElementById('more-options-menu').classList.add('hidden');
    };

    window.toggleSearch = function() {
        // Implementation for search functionality
        console.log('Toggle search');
    };

    // Copy room code with enhanced feedback
    window.copyRoomCode = function() {
        const roomCode = '{{ $chatRoom->room_code }}';
        navigator.clipboard.writeText(roomCode).then(() => {
            showNotification('کد روم کپی شد!', 'success');
        }).catch(() => {
            showNotification('خطا در کپی کردن کد', 'error');
        });
    };
}

function showTypingIndicator() {
    const indicator = document.getElementById('typing-indicator');
    indicator.classList.remove('hidden');
    indicator.classList.add('animate-fade-in');
}

function hideTypingIndicator() {
    const indicator = document.getElementById('typing-indicator');
    indicator.classList.add('hidden');
    indicator.classList.remove('animate-fade-in');
}

// Enhanced message animations
function addMessageWithAnimation(messageElement) {
    messageElement.classList.add('animate-fade-in');
    setTimeout(() => {
        messageElement.classList.remove('animate-fade-in');
    }, 500);
}

// Sound effects (optional)
function playNotificationSound() {
    // You can add sound effects here
    // const audio = new Audio('/sounds/notification.mp3');
    // audio.play().catch(() => {});
}
</script>
<script src="{{ asset('js/chat.js') }}"></script>
@endpush
@endsection
