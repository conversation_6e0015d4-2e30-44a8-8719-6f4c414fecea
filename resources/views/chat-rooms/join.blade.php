@extends('layouts.app')

@section('title', 'پیوستن به چت روم')

@section('content')
<div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="py-6">
        <div class="md:flex md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                    پیوستن به چت روم
                </h2>
            </div>
            <div class="mt-4 flex md:mt-0 md:mr-4">
                <a href="{{ route('dashboard') }}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    بازگشت به داشبورد
                </a>
            </div>
        </div>
    </div>

    <div class="bg-white shadow rounded-lg">
        <div class="p-6">
            <form action="{{ route('chat-rooms.join.post') }}" method="POST" class="space-y-6">
                @csrf
                
                <div>
                    <label for="room_code" class="block text-sm font-medium text-gray-700">کد چت روم</label>
                    <input type="text" name="room_code" id="room_code" required
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('room_code') border-red-500 @enderror"
                           placeholder="کد ۸ رقمی چت روم را وارد کنید"
                           value="{{ old('room_code') }}"
                           maxlength="8">
                    @error('room_code')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-sm text-gray-500">کد چت روم را از سازنده آن دریافت کنید</p>
                </div>

                <div id="password-section" class="hidden">
                    <label for="password" class="block text-sm font-medium text-gray-700">رمز عبور چت روم</label>
                    <input type="password" name="password" id="password"
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('password') border-red-500 @enderror"
                           placeholder="رمز عبور چت روم">
                    @error('password')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="flex justify-end space-x-3 space-x-reverse">
                    <a href="{{ route('dashboard') }}" 
                       class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        انصراف
                    </a>
                    <button type="submit" 
                            class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        پیوستن به روم
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Room Preview Section -->
    <div id="room-preview" class="hidden mt-6 bg-white shadow rounded-lg">
        <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">پیش‌نمایش چت روم</h3>
            <div id="room-info">
                <!-- Room information will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const roomCodeInput = document.getElementById('room_code');
    const passwordSection = document.getElementById('password-section');
    const roomPreview = document.getElementById('room-preview');
    const roomInfo = document.getElementById('room-info');
    
    let debounceTimer;
    
    roomCodeInput.addEventListener('input', function() {
        const roomCode = this.value.trim();
        
        // Clear previous timer
        clearTimeout(debounceTimer);
        
        // Hide sections
        passwordSection.classList.add('hidden');
        roomPreview.classList.add('hidden');
        
        if (roomCode.length === 8) {
            // Debounce the API call
            debounceTimer = setTimeout(() => {
                checkRoomCode(roomCode);
            }, 500);
        }
    });
    
    function checkRoomCode(roomCode) {
        fetch(`/api/chat-rooms/check/${roomCode}`, {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show room preview
                roomInfo.innerHTML = `
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="flex-shrink-0">
                            <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-lg font-medium text-gray-900">${data.room.name}</h4>
                            <p class="text-sm text-gray-500">${data.room.description || 'بدون توضیحات'}</p>
                            <div class="mt-1 text-sm text-gray-500">
                                ${data.room.member_count} عضو • ${data.room.is_private ? 'خصوصی' : 'عمومی'}
                            </div>
                        </div>
                    </div>
                `;
                roomPreview.classList.remove('hidden');
                
                // Show password field if room is private
                if (data.room.is_private) {
                    passwordSection.classList.remove('hidden');
                    document.getElementById('password').required = true;
                }
            } else {
                // Show error in room info
                roomInfo.innerHTML = `
                    <div class="text-center py-4">
                        <svg class="mx-auto h-12 w-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">چت روم یافت نشد</h3>
                        <p class="mt-1 text-sm text-gray-500">کد وارد شده معتبر نیست یا چت روم وجود ندارد.</p>
                    </div>
                `;
                roomPreview.classList.remove('hidden');
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }
});
</script>
@endsection
