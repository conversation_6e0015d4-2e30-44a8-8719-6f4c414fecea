@php
    $isOwn = $message->user_id === auth()->id();
    $isEdited = $message->is_edited;
    $isDeleted = $message->is_deleted;
@endphp

<div class="flex {{ $isOwn ? 'justify-end' : 'justify-start' }} group hover:bg-white/30 rounded-2xl p-2 transition-all duration-200">
    <div class="flex {{ $isOwn ? 'flex-row-reverse' : 'flex-row' }} items-end space-x-3 space-x-reverse max-w-xs lg:max-w-lg">
        @if(!$isOwn)
            <div class="flex-shrink-0 relative">
                <div class="h-10 w-10 rounded-2xl bg-gradient-to-br from-indigo-400 to-purple-500 flex items-center justify-center shadow-lg ring-2 ring-white">
                    <span class="text-sm font-bold text-white">
                        {{ substr($message->user->name, 0, 1) }}
                    </span>
                </div>
                @if($message->user->isOnline())
                    <div class="absolute -bottom-1 -left-1 h-4 w-4 bg-green-400 border-2 border-white rounded-full"></div>
                @endif
            </div>
        @endif

        <div class="flex flex-col {{ $isOwn ? 'items-end' : 'items-start' }} max-w-full">
            @if(!$isOwn)
                <div class="flex items-center space-x-2 space-x-reverse mb-1 {{ $isOwn ? 'mr-3' : 'ml-3' }}">
                    <span class="text-sm font-semibold text-gray-700">
                        {{ $message->user->name }}
                    </span>
                    <span class="text-xs text-gray-400">
                        {{ $message->created_at->format('H:i') }}
                    </span>
                </div>
            @endif
            
            <!-- Reply Preview -->
            @if($message->replyTo)
                <div class="mb-3 p-3 bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl border-r-4 border-indigo-400 text-sm max-w-full shadow-sm">
                    <div class="flex items-center space-x-2 space-x-reverse text-xs text-indigo-600 font-medium mb-1">
                        <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                        </svg>
                        پاسخ به {{ $message->replyTo->user->name }}
                    </div>
                    <div class="text-gray-700 line-clamp-2 text-sm">
                        {{ Str::limit($message->replyTo->content, 80) }}
                    </div>
                </div>
            @endif

            <div class="relative group/message">
                @if($isDeleted)
                    <div class="px-4 py-3 rounded-2xl bg-gray-100 text-gray-500 italic border border-gray-200 shadow-sm">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            این پیام حذف شده است
                        </div>
                    </div>
                @else
                    @if($message->type === 'file')
                        <!-- File Message with Modern Design -->
                        <div class="px-5 py-4 rounded-2xl {{ $isOwn ? 'bg-gradient-to-br from-indigo-500 to-purple-600 text-white shadow-lg' : 'bg-white border border-gray-200 shadow-sm' }} max-w-sm">
                            <div class="flex items-center space-x-3 space-x-reverse">
                                <div class="flex-shrink-0">
                                    <div class="h-12 w-12 rounded-xl {{ $isOwn ? 'bg-white/20' : 'bg-indigo-100' }} flex items-center justify-center">
                                        <svg class="h-6 w-6 {{ $isOwn ? 'text-white' : 'text-indigo-600' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <a href="{{ Storage::url($message->file_path) }}"
                                       target="_blank"
                                       class="{{ $isOwn ? 'text-white hover:text-indigo-100' : 'text-indigo-600 hover:text-indigo-800' }} font-semibold text-sm hover:underline truncate block">
                                        {{ $message->file_name }}
                                    </a>
                                    <div class="text-xs {{ $isOwn ? 'text-indigo-100' : 'text-gray-500' }} mt-1">
                                        📎 {{ formatFileSize($message->file_size ?? 0) }}
                                    </div>
                                </div>
                            </div>
                            @if($message->content && $message->content !== 'فایل ارسال شد: ' . $message->file_name)
                                <div class="mt-3 pt-3 border-t {{ $isOwn ? 'border-white/20' : 'border-gray-200' }}">
                                    <div class="text-sm whitespace-pre-wrap break-words">
                                        {{ $message->content }}
                                    </div>
                                </div>
                            @endif
                        </div>
                    @else
                        <!-- Text Message with Modern Design -->
                        <div class="px-5 py-3 rounded-2xl {{ $isOwn ? 'bg-gradient-to-br from-indigo-500 to-purple-600 text-white shadow-lg' : 'bg-white border border-gray-200 shadow-sm hover:shadow-md' }} transition-all duration-200 max-w-md">
                            <div class="text-sm whitespace-pre-wrap break-words leading-relaxed">
                                {{ $message->content }}
                            </div>
                            @if($isEdited)
                                <div class="mt-2 pt-2 border-t {{ $isOwn ? 'border-white/20' : 'border-gray-200' }}">
                                    <span class="text-xs {{ $isOwn ? 'text-indigo-100' : 'text-gray-500' }} italic flex items-center">
                                        <svg class="h-3 w-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                        ویرایش شده
                                    </span>
                                </div>
                            @endif
                        </div>
                    @endif
                @endif
                
                <!-- Message Actions with Modern Design -->
                @if(!$isDeleted)
                    <div class="absolute {{ $isOwn ? 'left-0 -ml-24' : 'right-0 -mr-24' }} top-1/2 transform -translate-y-1/2 opacity-0 group-hover/message:opacity-100 transition-all duration-300 ease-out scale-90 group-hover/message:scale-100">
                        <div class="flex items-center space-x-1 space-x-reverse bg-white/90 backdrop-blur-sm border border-gray-200/50 rounded-2xl shadow-lg p-2">
                            <!-- Reply Button -->
                            <button onclick="replyToMessage({{ $message->id }}, '{{ addslashes($message->content) }}')"
                                    class="group/btn p-2 text-gray-400 hover:text-indigo-600 rounded-xl hover:bg-indigo-50 transition-all duration-200 hover:scale-110"
                                    title="پاسخ">
                                <svg class="h-4 w-4 group-hover/btn:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                </svg>
                            </button>

                            <!-- Reaction Button -->
                            <button onclick="showReactionPicker({{ $message->id }})"
                                    class="group/btn p-2 text-gray-400 hover:text-yellow-500 rounded-xl hover:bg-yellow-50 transition-all duration-200 hover:scale-110"
                                    title="واکنش">
                                <svg class="h-4 w-4 group-hover/btn:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </button>

                            @if($isOwn && !$isDeleted)
                                <!-- Edit Button (only for text messages and within 15 minutes) -->
                                @if(($message->type === 'text' || !$message->type) && $message->created_at->diffInMinutes(now()) <= 15)
                                    <button onclick="editMessage({{ $message->id }}, '{{ addslashes($message->content) }}')"
                                            class="group/btn p-2 text-gray-400 hover:text-green-600 rounded-xl hover:bg-green-50 transition-all duration-200 hover:scale-110"
                                            title="ویرایش">
                                        <svg class="h-4 w-4 group-hover/btn:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </button>
                                @endif

                                <!-- Delete Button -->
                                <button onclick="deleteMessage({{ $message->id }})"
                                        class="group/btn p-2 text-gray-400 hover:text-red-500 rounded-xl hover:bg-red-50 transition-all duration-200 hover:scale-110"
                                        title="حذف">
                                    <svg class="h-4 w-4 group-hover/btn:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            @endif
                        </div>
                    </div>
                @endif
            </div>

            <!-- Message Info with Enhanced Design -->
            @if($isOwn)
                <div class="flex items-center justify-end space-x-2 space-x-reverse mt-2 text-xs">
                    <div class="flex items-center space-x-1 space-x-reverse text-gray-500">
                        <span>{{ $message->created_at->format('H:i') }}</span>
                        @if($isEdited)
                            <span class="flex items-center">
                                <svg class="h-3 w-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                ویرایش شده
                            </span>
                        @endif
                    </div>
                    <!-- Message Status -->
                    <div class="flex items-center">
                        <svg class="h-4 w-4 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <svg class="h-4 w-4 text-indigo-600 -mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                </div>
            @else
                <div class="flex items-center space-x-2 space-x-reverse mt-2 mr-3 text-xs text-gray-500">
                    <span>{{ $message->created_at->format('H:i') }}</span>
                    @if($isEdited)
                        <span class="flex items-center">
                            <svg class="h-3 w-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            ویرایش شده
                        </span>
                    @endif
                </div>
            @endif
        </div>
    </div>
</div>

@php
    // Helper function for file size formatting
    if (!function_exists('formatFileSize')) {
        function formatFileSize($bytes) {
            if ($bytes >= 1073741824) {
                return number_format($bytes / 1073741824, 2) . ' GB';
            } elseif ($bytes >= 1048576) {
                return number_format($bytes / 1048576, 2) . ' MB';
            } elseif ($bytes >= 1024) {
                return number_format($bytes / 1024, 2) . ' KB';
            } else {
                return $bytes . ' bytes';
            }
        }
    }
@endphp
