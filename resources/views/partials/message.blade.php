@php
    $isOwn = $message->user_id === auth()->id();
    $isEdited = $message->is_edited;
    $isDeleted = $message->is_deleted;
@endphp

<div class="flex {{ $isOwn ? 'justify-end' : 'justify-start' }} group">
    <div class="flex {{ $isOwn ? 'flex-row-reverse' : 'flex-row' }} items-end space-x-2 space-x-reverse max-w-xs lg:max-w-md">
        @if(!$isOwn)
            <div class="flex-shrink-0">
                <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                    <span class="text-sm font-medium text-gray-700">
                        {{ substr($message->user->name, 0, 1) }}
                    </span>
                </div>
            </div>
        @endif
        
        <div class="flex flex-col {{ $isOwn ? 'items-end' : 'items-start' }}">
            @if(!$isOwn)
                <span class="text-xs text-gray-500 mb-1 {{ $isOwn ? 'mr-2' : 'ml-2' }}">
                    {{ $message->user->name }}
                </span>
            @endif
            
            <!-- Reply Preview -->
            @if($message->replyTo)
                <div class="mb-2 p-2 bg-gray-100 rounded-lg border-r-4 border-gray-400 text-sm max-w-full">
                    <div class="text-xs text-gray-600 mb-1">
                        پاسخ به {{ $message->replyTo->user->name }}:
                    </div>
                    <div class="text-gray-800 truncate">
                        {{ Str::limit($message->replyTo->content, 50) }}
                    </div>
                </div>
            @endif
            
            <div class="relative">
                @if($isDeleted)
                    <div class="px-4 py-2 rounded-lg bg-gray-200 text-gray-500 italic">
                        این پیام حذف شده است
                    </div>
                @else
                    @if($message->type === 'file')
                        <!-- File Message -->
                        <div class="px-4 py-2 rounded-lg {{ $isOwn ? 'bg-blue-600 text-white' : 'bg-white border border-gray-200' }}">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <svg class="h-5 w-5 {{ $isOwn ? 'text-blue-200' : 'text-gray-400' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                </svg>
                                <div>
                                    <a href="{{ Storage::url($message->file_path) }}" 
                                       target="_blank" 
                                       class="{{ $isOwn ? 'text-blue-100 hover:text-white' : 'text-blue-600 hover:text-blue-800' }} font-medium">
                                        {{ $message->file_name }}
                                    </a>
                                    <div class="text-xs {{ $isOwn ? 'text-blue-200' : 'text-gray-500' }}">
                                        {{ $this->formatFileSize($message->file_size) }}
                                    </div>
                                </div>
                            </div>
                            @if($message->content)
                                <div class="mt-2 text-sm">
                                    {{ $message->content }}
                                </div>
                            @endif
                        </div>
                    @else
                        <!-- Text Message -->
                        <div class="px-4 py-2 rounded-lg {{ $isOwn ? 'bg-blue-600 text-white' : 'bg-white border border-gray-200' }}">
                            <div class="text-sm whitespace-pre-wrap break-words">
                                {{ $message->content }}
                            </div>
                        </div>
                    @endif
                @endif
                
                <!-- Message Actions (visible on hover) -->
                @if(!$isDeleted)
                    <div class="absolute top-0 {{ $isOwn ? 'left-0 -ml-20' : 'right-0 -mr-20' }} opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <div class="flex items-center space-x-1 space-x-reverse bg-white border border-gray-200 rounded-lg shadow-sm p-1">
                            <!-- Reply Button -->
                            <button onclick="replyToMessage({{ $message->id }}, '{{ addslashes($message->content) }}')" 
                                    class="p-1 text-gray-400 hover:text-gray-600 rounded">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                </svg>
                            </button>
                            
                            @if($isOwn && !$isDeleted)
                                <!-- Edit Button (only for text messages and within 15 minutes) -->
                                @if($message->type === 'text' && $message->created_at->diffInMinutes(now()) <= 15)
                                    <button onclick="editMessage({{ $message->id }}, '{{ addslashes($message->content) }}')" 
                                            class="p-1 text-gray-400 hover:text-gray-600 rounded">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </button>
                                @endif
                                
                                <!-- Delete Button -->
                                <button onclick="deleteMessage({{ $message->id }})" 
                                        class="p-1 text-red-400 hover:text-red-600 rounded">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            @endif
                        </div>
                    </div>
                @endif
            </div>
            
            <!-- Message Info -->
            <div class="flex items-center space-x-2 space-x-reverse mt-1 text-xs text-gray-500 {{ $isOwn ? 'mr-2' : 'ml-2' }}">
                <span>{{ $message->created_at->format('H:i') }}</span>
                @if($isEdited)
                    <span>• ویرایش شده</span>
                @endif
                @if($isOwn)
                    <span>• ارسال شده</span>
                @endif
            </div>
        </div>
    </div>
</div>

@php
    // Helper function for file size formatting
    if (!function_exists('formatFileSize')) {
        function formatFileSize($bytes) {
            if ($bytes >= 1073741824) {
                return number_format($bytes / 1073741824, 2) . ' GB';
            } elseif ($bytes >= 1048576) {
                return number_format($bytes / 1048576, 2) . ' MB';
            } elseif ($bytes >= 1024) {
                return number_format($bytes / 1024, 2) . ' KB';
            } else {
                return $bytes . ' bytes';
            }
        }
    }
@endphp
