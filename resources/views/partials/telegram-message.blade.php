@php
    $isOwn = $message->user_id === auth()->id();
    $isEdited = $message->is_edited;
    $isDeleted = $message->is_deleted;
    $prevMessage = $prevMessage ?? null;
    $nextMessage = $nextMessage ?? null;
    
    // Check if this message should be grouped with previous message
    $shouldGroup = $prevMessage && 
                   $prevMessage->user_id === $message->user_id && 
                   $prevMessage->created_at->diffInMinutes($message->created_at) < 5;
@endphp

<div class="message-wrapper {{ $isOwn ? 'own-message' : 'other-message' }}" data-message-id="{{ $message->id }}">
    @if($isDeleted)
        <!-- Deleted Message -->
        <div class="flex {{ $isOwn ? 'justify-end' : 'justify-start' }} mb-1">
            <div class="max-w-xs sm:max-w-md lg:max-w-lg">
                <div class="bg-gray-100 rounded-2xl px-4 py-2 italic text-gray-500 text-sm">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7"></path>
                        </svg>
                        این پیام حذف شده است
                    </div>
                </div>
            </div>
        </div>
    @else
        <!-- Regular Message -->
        <div class="flex {{ $isOwn ? 'justify-end' : 'justify-start' }} group hover:bg-black/5 rounded-lg p-1 transition-colors duration-200 {{ $shouldGroup ? 'mb-0.5' : 'mb-2' }}">
            <div class="flex {{ $isOwn ? 'flex-row-reverse' : 'flex-row' }} items-end space-x-2 space-x-reverse max-w-xs sm:max-w-md lg:max-w-lg relative">

                @if(!$isOwn)
                    <!-- User Avatar (only show if not grouped or first message) -->
                    <div class="flex-shrink-0 {{ $shouldGroup ? 'invisible' : '' }}">
                        <div class="w-8 h-8 rounded-full bg-gradient-to-br from-blue-400 to-indigo-500 flex items-center justify-center text-white text-sm font-medium shadow-sm">
                            {{ substr($message->user->name, 0, 1) }}
                        </div>
                    </div>
                @endif

                <div class="flex flex-col {{ $isOwn ? 'items-end' : 'items-start' }} max-w-full">
                    @if(!$isOwn && !$shouldGroup)
                        <!-- User Name -->
                        <div class="text-xs font-medium text-gray-600 mb-1 px-1">
                            {{ $message->user->name }}
                        </div>
                    @endif

                    @if($message->replyTo)
                        <!-- Reply Preview -->
                        <div class="mb-1 {{ $isOwn ? 'mr-2' : 'ml-2' }}">
                            <div class="bg-gray-100 border-l-2 {{ $isOwn ? 'border-blue-500' : 'border-gray-400' }} rounded-r-lg px-3 py-2 text-xs max-w-full">
                                <div class="font-medium text-gray-600 mb-1">
                                    {{ $message->replyTo->user->name }}
                                </div>
                                <div class="text-gray-700 line-clamp-2">
                                    {{ Str::limit($message->replyTo->content, 80) }}
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Message Content -->
                    <div class="relative flex {{ $isOwn ? 'flex-row-reverse' : 'flex-row' }} items-start space-x-3 space-x-reverse">
                        <!-- Action Buttons (next to message) -->
                        <div class="flex {{ $isOwn ? 'flex-row-reverse' : 'flex-row' }} items-center space-x-2 space-x-reverse opacity-0 group-hover:opacity-100 transition-opacity duration-200 mt-2">
                            <!-- Reply Button -->
                            <button onclick="replyToMessage({{ $message->id }}, '{{ addslashes($message->content) }}')"
                                    class="p-2 text-gray-400 hover:text-blue-500 rounded-full hover:bg-blue-50 transition-colors"
                                    title="پاسخ">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                </svg>
                            </button>

                            @if($isOwn)
                                <!-- Three Dots Menu Button -->
                                <div class="relative">
                                    <button onclick="toggleMessageMenu({{ $message->id }})"
                                            class="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"
                                            title="گزینه‌ها">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                        </svg>
                                    </button>

                                    <!-- Dropdown Menu -->
                                    <div id="message-menu-{{ $message->id }}" class="hidden absolute {{ $isOwn ? 'left-0' : 'right-0' }} top-full mt-2 w-36 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                                        @if(($message->type === 'text' || !$message->type) && $message->created_at->diffInMinutes(now()) <= 15)
                                            <button onclick="editMessage({{ $message->id }}, '{{ addslashes($message->content) }}'); toggleMessageMenu({{ $message->id }});"
                                                    class="w-full text-right px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 flex items-center transition-colors">
                                                <svg class="w-4 h-4 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                                ویرایش
                                            </button>
                                        @endif
                                        <button onclick="deleteMessage({{ $message->id }}); toggleMessageMenu({{ $message->id }});"
                                                class="w-full text-right px-4 py-3 text-sm text-red-600 hover:bg-red-50 flex items-center transition-colors">
                                            <svg class="w-4 h-4 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7"></path>
                                            </svg>
                                            حذف
                                        </button>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <!-- Message Bubble with Menu -->
                        <div class="relative">
                            @if($message->type === 'file')
                                @php
                                    $isImage = str_contains($message->file_name, '.jpg') ||
                                              str_contains($message->file_name, '.png') ||
                                              str_contains($message->file_name, '.jpeg') ||
                                              str_contains($message->file_name, '.gif') ||
                                              str_contains($message->file_name, '.webp');
                                @endphp

                                @if($isImage)
                                    <!-- Image Message -->
                                    <div class="bg-white rounded-2xl {{ $isOwn ? 'rounded-br-md' : 'rounded-bl-md' }} shadow-sm border border-gray-200 overflow-hidden max-w-sm">
                                        <a href="{{ Storage::url($message->file_path) }}" target="_blank" class="block">
                                            <img src="{{ Storage::url($message->file_path) }}"
                                                 alt="{{ $message->file_name }}"
                                                 class="w-full h-auto max-h-80 object-cover hover:opacity-90 transition-opacity">
                                        </a>
                                        @if($message->content && !str_contains($message->content, 'فایل ارسال شد:'))
                                            <div class="p-3">
                                                <div class="text-sm text-gray-700 whitespace-pre-wrap break-words">
                                                    {{ $message->content }}
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                @else
                                    <!-- File Message -->
                                    <div class="bg-white rounded-2xl {{ $isOwn ? 'rounded-br-md' : 'rounded-bl-md' }} shadow-sm border border-gray-200 p-4 min-w-48">
                                        <div class="flex items-center space-x-3 space-x-reverse">
                                            <div class="flex-shrink-0">
                                                <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                                                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <a href="{{ Storage::url($message->file_path) }}"
                                                   target="_blank"
                                                   class="text-blue-600 hover:text-blue-800 font-medium text-sm truncate block">
                                                    {{ $message->file_name }}
                                                </a>
                                                <div class="text-xs text-gray-500 mt-1">
                                                    {{ formatFileSize($message->file_size ?? 0) }}
                                                </div>
                                            </div>
                                            <div class="flex-shrink-0">
                                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                            </div>
                                        </div>
                                        @if($message->content && !str_contains($message->content, 'فایل ارسال شد:'))
                                            <div class="mt-3 pt-3 border-t border-gray-100">
                                                <div class="text-sm text-gray-700 whitespace-pre-wrap break-words">
                                                    {{ $message->content }}
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                @endif
                            @else
                                <!-- Text Message -->
                                <div class="{{ $isOwn ? 'bg-blue-500 text-white rounded-2xl rounded-br-md' : 'bg-white text-gray-900 rounded-2xl rounded-bl-md shadow-sm border border-gray-200' }} px-4 py-2 relative">
                                    <div class="text-sm whitespace-pre-wrap break-words leading-relaxed">
                                        {{ $message->content }}
                                    </div>

                                    <!-- Message Time & Status -->
                                    <div class="flex items-center justify-end space-x-1 space-x-reverse mt-1 text-xs {{ $isOwn ? 'text-blue-100' : 'text-gray-500' }}">
                                        @if($isEdited)
                                            <span class="italic">ویرایش شده</span>
                                            <span>•</span>
                                        @endif
                                        <span>{{ $message->created_at->format('H:i') }}</span>
                                        @if($isOwn)
                                            <!-- Message Status Icons -->
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 {{ $isOwn ? 'text-blue-200' : 'text-gray-400' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                                <svg class="w-4 h-4 {{ $isOwn ? 'text-blue-100' : 'text-gray-300' }} -mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

@php
    // Helper function for file size formatting
    if (!function_exists('formatFileSize')) {
        function formatFileSize($bytes) {
            if ($bytes >= 1073741824) {
                return number_format($bytes / 1073741824, 2) . ' GB';
            } elseif ($bytes >= 1048576) {
                return number_format($bytes / 1048576, 2) . ' MB';
            } elseif ($bytes >= 1024) {
                return number_format($bytes / 1024, 2) . ' KB';
            } else {
                return $bytes . ' bytes';
            }
        }
    }
@endphp
