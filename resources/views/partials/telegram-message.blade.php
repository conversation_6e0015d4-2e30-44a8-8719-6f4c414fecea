@php
    $isOwn = $message->user_id === auth()->id();
    $isEdited = $message->is_edited;
    $isDeleted = $message->is_deleted;
    $prevMessage = $prevMessage ?? null;
    $nextMessage = $nextMessage ?? null;
    
    // Check if this message should be grouped with previous message
    $shouldGroup = $prevMessage && 
                   $prevMessage->user_id === $message->user_id && 
                   $prevMessage->created_at->diffInMinutes($message->created_at) < 5;
@endphp

<div class="message-wrapper {{ $isOwn ? 'own-message' : 'other-message' }}" data-message-id="{{ $message->id }}">
    @if($isDeleted)
        <!-- Deleted Message -->
        <div class="flex {{ $isOwn ? 'justify-end' : 'justify-start' }} mb-1">
            <div class="max-w-xs sm:max-w-md lg:max-w-lg">
                <div class="bg-gray-100 rounded-2xl px-4 py-2 italic text-gray-500 text-sm">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7"></path>
                        </svg>
                        این پیام حذف شده است
                    </div>
                </div>
            </div>
        </div>
    @else
        <!-- Regular Message -->
        <div class="flex {{ $isOwn ? 'justify-end' : 'justify-start' }} group hover:bg-black/5 rounded-lg p-1 transition-colors duration-200 {{ $shouldGroup ? 'mb-0.5' : 'mb-2' }}">
            <div class="flex {{ $isOwn ? 'flex-row-reverse' : 'flex-row' }} items-end space-x-2 space-x-reverse max-w-xs sm:max-w-md lg:max-w-lg relative">

                @if(!$isOwn)
                    <!-- User Avatar (only show if not grouped or first message) -->
                    <div class="flex-shrink-0 {{ $shouldGroup ? 'invisible' : '' }}">
                        <div class="w-8 h-8 rounded-full bg-gradient-to-br from-blue-400 to-indigo-500 flex items-center justify-center text-white text-sm font-medium shadow-sm">
                            {{ substr($message->user->name, 0, 1) }}
                        </div>
                    </div>
                @endif

                <div class="flex flex-col {{ $isOwn ? 'items-end' : 'items-start' }} max-w-full">
                    @if(!$isOwn && !$shouldGroup)
                        <!-- User Name -->
                        <div class="text-xs font-medium text-gray-600 mb-1 px-1">
                            {{ $message->user->name }}
                        </div>
                    @endif

                    @if($message->replyTo)
                        <!-- Reply Preview -->
                        <div class="mb-1 {{ $isOwn ? 'mr-2' : 'ml-2' }}">
                            <div class="bg-gray-100 border-l-2 {{ $isOwn ? 'border-blue-500' : 'border-gray-400' }} rounded-r-lg px-3 py-2 text-xs max-w-full">
                                <div class="font-medium text-gray-600 mb-1">
                                    {{ $message->replyTo->user->name }}
                                </div>
                                <div class="text-gray-700 line-clamp-2">
                                    {{ Str::limit($message->replyTo->content, 80) }}
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Message Content -->
                    <div class="relative">
                        <!-- Message Actions (positioned outside message bubble) -->
                        <div class="absolute {{ $isOwn ? '-left-14' : '-right-14' }} top-0 opacity-0 group-hover:opacity-100 transition-all duration-200 z-10">
                            <div class="flex items-center space-x-1 space-x-reverse bg-white rounded-full shadow-lg border border-gray-200 p-1">
                                <!-- Reply -->
                                <button onclick="replyToMessage({{ $message->id }}, '{{ addslashes($message->content) }}')"
                                        class="p-1.5 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded-full transition-colors"
                                        title="پاسخ">
                                    <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                    </svg>
                                </button>

                                @if($isOwn)
                                    <!-- Edit (only for text messages within 15 minutes) -->
                                    @if(($message->type === 'text' || !$message->type) && $message->created_at->diffInMinutes(now()) <= 15)
                                        <button onclick="editMessage({{ $message->id }}, '{{ addslashes($message->content) }}')"
                                                class="p-1.5 text-gray-400 hover:text-green-500 hover:bg-green-50 rounded-full transition-colors"
                                                title="ویرایش">
                                            <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                        </button>
                                    @endif

                                    <!-- Delete -->
                                    <button onclick="deleteMessage({{ $message->id }})"
                                            class="p-1.5 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-full transition-colors"
                                            title="حذف">
                                        <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7"></path>
                                        </svg>
                                    </button>
                                @endif
                            </div>
                        </div>

                        @if($message->type === 'file')
                            <!-- File Message -->
                            <div class="bg-white rounded-2xl {{ $isOwn ? 'rounded-br-md' : 'rounded-bl-md' }} shadow-sm border border-gray-200 p-4 min-w-48">
                                <div class="flex items-center space-x-3 space-x-reverse">
                                    <div class="flex-shrink-0">
                                        <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                                            @if(str_contains($message->file_name, '.jpg') || str_contains($message->file_name, '.png') || str_contains($message->file_name, '.jpeg'))
                                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                </svg>
                                            @else
                                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <a href="{{ Storage::url($message->file_path) }}" 
                                           target="_blank" 
                                           class="text-blue-600 hover:text-blue-800 font-medium text-sm truncate block">
                                            {{ $message->file_name }}
                                        </a>
                                        <div class="text-xs text-gray-500 mt-1">
                                            {{ formatFileSize($message->file_size ?? 0) }}
                                        </div>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </div>
                                </div>
                                @if($message->content && $message->content !== 'فایل ارسال شد: ' . $message->file_name)
                                    <div class="mt-3 pt-3 border-t border-gray-100">
                                        <div class="text-sm text-gray-700 whitespace-pre-wrap break-words">
                                            {{ $message->content }}
                                        </div>
                                    </div>
                                @endif
                            </div>
                        @else
                            <!-- Text Message -->
                            <div class="{{ $isOwn ? 'bg-blue-500 text-white rounded-2xl rounded-br-md' : 'bg-white text-gray-900 rounded-2xl rounded-bl-md shadow-sm border border-gray-200' }} px-4 py-2 relative">
                                <div class="text-sm whitespace-pre-wrap break-words leading-relaxed">
                                    {{ $message->content }}
                                </div>

                                <!-- Message Time & Status -->
                                <div class="flex items-center justify-end space-x-1 space-x-reverse mt-1 text-xs {{ $isOwn ? 'text-blue-100' : 'text-gray-500' }}">
                                    @if($isEdited)
                                        <span class="italic">ویرایش شده</span>
                                        <span>•</span>
                                    @endif
                                    <span>{{ $message->created_at->format('H:i') }}</span>
                                    @if($isOwn)
                                        <!-- Message Status Icons -->
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 {{ $isOwn ? 'text-blue-200' : 'text-gray-400' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <svg class="w-4 h-4 {{ $isOwn ? 'text-blue-100' : 'text-gray-300' }} -mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

@php
    // Helper function for file size formatting
    if (!function_exists('formatFileSize')) {
        function formatFileSize($bytes) {
            if ($bytes >= 1073741824) {
                return number_format($bytes / 1073741824, 2) . ' GB';
            } elseif ($bytes >= 1048576) {
                return number_format($bytes / 1048576, 2) . ' MB';
            } elseif ($bytes >= 1024) {
                return number_format($bytes / 1024, 2) . ' KB';
            } else {
                return $bytes . ' bytes';
            }
        }
    }
@endphp
