@php
    $isOwn = $message->user_id === auth()->id();
    $isEdited = $message->is_edited;
    $isDeleted = $message->is_deleted;
    $prevMessage = $prevMessage ?? null;
    $nextMessage = $nextMessage ?? null;
    
    // Check if this message should be grouped with previous message
    $shouldGroup = $prevMessage && 
                   $prevMessage->user_id === $message->user_id && 
                   $prevMessage->created_at->diffInMinutes($message->created_at) < 5;
@endphp

<div class="message-wrapper {{ $isOwn ? 'own-message' : 'other-message' }}" data-message-id="{{ $message->id }}">
    @if($isDeleted)
        <!-- Deleted Message -->
        <div class="flex {{ $isOwn ? 'justify-end' : 'justify-start' }} mb-2">
            <div class="max-w-xs sm:max-w-md lg:max-w-lg">
                <div class="bg-white/10 backdrop-blur-xl rounded-3xl px-5 py-3 italic text-white/60 text-sm border border-white/20">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7"></path>
                        </svg>
                        این پیام حذف شده است
                    </div>
                </div>
            </div>
        </div>
    @else
        <!-- Ultra Modern Message -->
        <div class="flex {{ $isOwn ? 'justify-end' : 'justify-start' }} group hover:bg-white/5 rounded-2xl p-2 transition-all duration-300 {{ $shouldGroup ? 'mb-1' : 'mb-3' }}">
            <div class="flex {{ $isOwn ? 'flex-row-reverse' : 'flex-row' }} items-end space-x-3 space-x-reverse max-w-xs sm:max-w-md lg:max-w-2xl">
                
                @if(!$isOwn)
                    <!-- User Avatar (only show if not grouped or first message) -->
                    <div class="flex-shrink-0 {{ $shouldGroup ? 'invisible' : '' }}">
                        <div class="w-10 h-10 rounded-2xl bg-gradient-to-br from-cyan-400 via-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-bold shadow-xl ring-2 ring-white/20">
                            {{ substr($message->user->name, 0, 1) }}
                        </div>
                        <!-- Glow Effect -->
                        <div class="absolute w-10 h-10 rounded-2xl bg-gradient-to-br from-cyan-400 via-blue-500 to-purple-600 opacity-30 blur-lg animate-pulse -z-10"></div>
                    </div>
                @endif
                
                <div class="flex flex-col {{ $isOwn ? 'items-end' : 'items-start' }} max-w-full">
                    @if(!$isOwn && !$shouldGroup)
                        <!-- User Name -->
                        <div class="text-xs font-semibold text-white/70 mb-2 px-2">
                            {{ $message->user->name }}
                        </div>
                    @endif
                    
                    @if($message->replyTo)
                        <!-- Reply Preview -->
                        <div class="mb-2 {{ $isOwn ? 'mr-2' : 'ml-2' }}">
                            <div class="bg-white/10 backdrop-blur-xl border-l-4 {{ $isOwn ? 'border-purple-400' : 'border-cyan-400' }} rounded-r-2xl px-4 py-2 text-xs max-w-full border border-white/10">
                                <div class="font-semibold text-white/80 mb-1 flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                    </svg>
                                    {{ $message->replyTo->user->name }}
                                </div>
                                <div class="text-white/60 line-clamp-2">
                                    {{ Str::limit($message->replyTo->content, 80) }}
                                </div>
                            </div>
                        </div>
                    @endif
                    
                    <!-- Message Content -->
                    <div class="relative group/message">
                        @if($message->type === 'file')
                            <!-- File Message -->
                            <div class="bg-white/90 backdrop-blur-xl rounded-3xl {{ $isOwn ? 'rounded-br-lg' : 'rounded-bl-lg' }} shadow-2xl border border-white/20 p-5 min-w-64">
                                <div class="flex items-center space-x-4 space-x-reverse">
                                    <div class="flex-shrink-0">
                                        <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                                            @if(str_contains($message->file_name, '.jpg') || str_contains($message->file_name, '.png') || str_contains($message->file_name, '.jpeg'))
                                                <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                </svg>
                                            @else
                                                <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <a href="{{ Storage::url($message->file_path) }}" 
                                           target="_blank" 
                                           class="text-blue-600 hover:text-blue-800 font-semibold text-sm truncate block hover:underline">
                                            {{ $message->file_name }}
                                        </a>
                                        <div class="text-xs text-gray-500 mt-1 flex items-center">
                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                                            </svg>
                                            {{ formatFileSize($message->file_size ?? 0) }}
                                        </div>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <button class="p-2 bg-gradient-to-r from-green-400 to-emerald-500 text-white rounded-xl hover:scale-105 transition-transform duration-200 shadow-lg">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                @if($message->content && $message->content !== 'فایل ارسال شد: ' . $message->file_name)
                                    <div class="mt-4 pt-4 border-t border-gray-100">
                                        <div class="text-sm text-gray-700 whitespace-pre-wrap break-words">
                                            {{ $message->content }}
                                        </div>
                                    </div>
                                @endif
                            </div>
                        @else
                            <!-- Text Message -->
                            <div class="{{ $isOwn ? 'bg-gradient-to-br from-purple-500 to-pink-500 text-white rounded-3xl rounded-br-lg shadow-2xl' : 'bg-white/90 backdrop-blur-xl text-gray-900 rounded-3xl rounded-bl-lg shadow-2xl border border-white/20' }} px-5 py-3 relative overflow-hidden">
                                <!-- Background Pattern for Own Messages -->
                                @if($isOwn)
                                    <div class="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-3xl"></div>
                                    <div class="absolute top-0 right-0 w-20 h-20 bg-white/5 rounded-full -mr-10 -mt-10"></div>
                                    <div class="absolute bottom-0 left-0 w-16 h-16 bg-white/5 rounded-full -ml-8 -mb-8"></div>
                                @endif
                                
                                <div class="relative z-10">
                                    <div class="text-sm whitespace-pre-wrap break-words leading-relaxed">
                                        {{ $message->content }}
                                    </div>
                                    
                                    <!-- Message Time & Status -->
                                    <div class="flex items-center justify-end space-x-2 space-x-reverse mt-2 text-xs {{ $isOwn ? 'text-white/70' : 'text-gray-500' }}">
                                        @if($isEdited)
                                            <span class="italic flex items-center">
                                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                                ویرایش شده
                                            </span>
                                            <span>•</span>
                                        @endif
                                        <span>{{ $message->created_at->format('H:i') }}</span>
                                        @if($isOwn)
                                            <!-- Message Status Icons -->
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 {{ $isOwn ? 'text-white/60' : 'text-gray-400' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                                <svg class="w-4 h-4 {{ $isOwn ? 'text-white/80' : 'text-gray-300' }} -mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endif
                        
                        <!-- Message Actions (Ultra Modern) -->
                        <div class="absolute {{ $isOwn ? 'left-0 -ml-20' : 'right-0 -mr-20' }} top-1/2 transform -translate-y-1/2 opacity-0 group-hover/message:opacity-100 transition-all duration-300 scale-90 group-hover/message:scale-100">
                            <div class="flex items-center space-x-1 space-x-reverse bg-black/60 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 p-2">
                                <!-- Reply -->
                                <button onclick="replyToMessage({{ $message->id }}, '{{ addslashes($message->content) }}')" 
                                        class="group/btn p-2 text-white/70 hover:text-cyan-400 hover:bg-cyan-400/20 rounded-xl transition-all duration-200"
                                        title="پاسخ">
                                    <svg class="w-4 h-4 group-hover/btn:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                    </svg>
                                </button>
                                
                                @if($isOwn)
                                    <!-- Edit (only for text messages within 15 minutes) -->
                                    @if(($message->type === 'text' || !$message->type) && $message->created_at->diffInMinutes(now()) <= 15)
                                        <button onclick="editMessage({{ $message->id }}, '{{ addslashes($message->content) }}')" 
                                                class="group/btn p-2 text-white/70 hover:text-green-400 hover:bg-green-400/20 rounded-xl transition-all duration-200"
                                                title="ویرایش">
                                            <svg class="w-4 h-4 group-hover/btn:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                        </button>
                                    @endif
                                    
                                    <!-- Delete -->
                                    <button onclick="deleteMessage({{ $message->id }})" 
                                            class="group/btn p-2 text-white/70 hover:text-red-400 hover:bg-red-400/20 rounded-xl transition-all duration-200"
                                            title="حذف">
                                        <svg class="w-4 h-4 group-hover/btn:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7"></path>
                                        </svg>
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

@php
    // Helper function for file size formatting
    if (!function_exists('formatFileSize')) {
        function formatFileSize($bytes) {
            if ($bytes >= 1073741824) {
                return number_format($bytes / 1073741824, 2) . ' GB';
            } elseif ($bytes >= 1048576) {
                return number_format($bytes / 1048576, 2) . ' MB';
            } elseif ($bytes >= 1024) {
                return number_format($bytes / 1024, 2) . ' KB';
            } else {
                return $bytes . ' bytes';
            }
        }
    }
@endphp
