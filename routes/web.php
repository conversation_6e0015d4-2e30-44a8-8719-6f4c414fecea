<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\ChatRoomController;
use App\Http\Controllers\MessageController;
use Illuminate\Support\Facades\Route;

// Public routes
Route::get('/', function () {
    return view('welcome');
})->name('home');

// Authentication routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);
});

Route::post('/logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');

// Protected routes
Route::middleware('auth')->group(function () {
    // Dashboard
    Route::get('/dashboard', [ChatRoomController::class, 'dashboard'])->name('dashboard');

    // Chat rooms
    Route::get('/chat-rooms/create', [ChatRoomController::class, 'create'])->name('chat-rooms.create');
    Route::post('/chat-rooms', [ChatRoomController::class, 'store'])->name('chat-rooms.store');
    Route::get('/chat-rooms/join', [ChatRoomController::class, 'showJoin'])->name('chat-rooms.join');
    Route::post('/chat-rooms/join', [ChatRoomController::class, 'join'])->name('chat-rooms.join.post');
    Route::get('/chat-rooms/{chatRoom}', [ChatRoomController::class, 'show'])->name('chat-rooms.show');
    Route::post('/chat-rooms/{chatRoom}/leave', [ChatRoomController::class, 'leave'])->name('chat-rooms.leave');

    // Messages
    Route::get('/chat-rooms/{chatRoom}/messages', [MessageController::class, 'index'])->name('messages.index');
    Route::post('/chat-rooms/{chatRoom}/messages', [MessageController::class, 'store'])->name('messages.store');
    Route::put('/messages/{message}', [MessageController::class, 'update'])->name('messages.update');
    Route::delete('/messages/{message}', [MessageController::class, 'destroy'])->name('messages.destroy');
    Route::post('/chat-rooms/{chatRoom}/upload', [MessageController::class, 'uploadFile'])->name('messages.upload');
});

// API routes for AJAX calls
Route::middleware('auth')->prefix('api')->group(function () {
    Route::get('/chat-rooms/check/{roomCode}', [ChatRoomController::class, 'checkRoomCode']);
    Route::post('/user/online', [AuthController::class, 'markOnline']);
    Route::post('/user/offline', [AuthController::class, 'markOffline']);
    Route::get('/chat-rooms/{chatRoom}/messages', [MessageController::class, 'index'])->name('api.messages.index');
});
