// Chat functionality
let socket = null;
let reconnectAttempts = 0;
const maxReconnectAttempts = 5;
let isConnected = false;

// Initialize chat
function initializeChat() {
    setupMessageForm();
    setupFileUpload();
    scrollToBottom();
    connectWebSocket();
    
    // Mark user as online
    markUserOnline();
    
    // Setup periodic online status update
    setInterval(markUserOnline, 30000); // Every 30 seconds
    
    // Setup beforeunload to mark user offline
    window.addEventListener('beforeunload', markUserOffline);
}

// WebSocket connection
function connectWebSocket() {
    try {
        // For development, we'll use polling instead of WebSocket initially
        startPolling();
    } catch (error) {
        console.error('WebSocket connection failed:', error);
        startPolling();
    }
}

// Polling fallback for real-time updates
function startPolling() {
    setInterval(fetchNewMessages, 3000); // Poll every 3 seconds
}

// Fetch new messages
async function fetchNewMessages() {
    try {
        const lastMessageId = getLastMessageId();
        const response = await fetch(`/chat-rooms/${chatRoomId}/messages?after=${lastMessageId}`, {
            headers: {
                'X-CSRF-TOKEN': csrfToken,
                'Accept': 'application/json',
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.messages && data.messages.length > 0) {
                appendNewMessages(data.messages);
            }
        }
    } catch (error) {
        console.error('Error fetching messages:', error);
    }
}

// Get last message ID
function getLastMessageId() {
    const messages = document.querySelectorAll('.message-item');
    if (messages.length > 0) {
        const lastMessage = messages[messages.length - 1];
        return lastMessage.dataset.messageId || 0;
    }
    return 0;
}

// Append new messages
function appendNewMessages(messages) {
    const container = document.getElementById('messages-container');
    const wasAtBottom = isScrolledToBottom();
    
    messages.forEach(message => {
        if (!document.querySelector(`[data-message-id="${message.id}"]`)) {
            const messageHtml = createMessageElement(message);
            container.insertAdjacentHTML('beforeend', messageHtml);
        }
    });
    
    if (wasAtBottom) {
        scrollToBottom();
    }
}

// Create message element HTML - Telegram Style
function createMessageElement(message) {
    const isOwn = message.user_id === currentUserId;
    const time = new Date(message.created_at).toLocaleTimeString('fa-IR', {
        hour: '2-digit',
        minute: '2-digit'
    });

    return `
        <div class="message-wrapper ${isOwn ? 'own-message' : 'other-message'}" data-message-id="${message.id}">
            <div class="flex ${isOwn ? 'justify-end' : 'justify-start'} group hover:bg-black/5 rounded-lg p-1 transition-colors duration-200 mb-2 relative">
                <!-- Message Actions -->
                <div class="absolute ${isOwn ? 'left-0 -ml-16' : 'right-0 -mr-16'} top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-all duration-200 z-10">
                    <div class="flex items-center space-x-1 space-x-reverse bg-white rounded-full shadow-lg border border-gray-200 p-1">
                        <button onclick="replyToMessage(${message.id}, '${message.content.replace(/'/g, "\\'")}')">
                            <svg class="w-4 h-4 text-gray-400 hover:text-blue-500 p-2 hover:bg-blue-50 rounded-full transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="flex ${isOwn ? 'flex-row-reverse' : 'flex-row'} items-end space-x-2 space-x-reverse max-w-xs sm:max-w-md lg:max-w-lg">
                    ${!isOwn ? `
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 rounded-full bg-gradient-to-br from-blue-400 to-indigo-500 flex items-center justify-center text-white text-sm font-medium shadow-sm">
                                ${message.user.name.charAt(0)}
                            </div>
                        </div>
                    ` : ''}

                    <div class="flex flex-col ${isOwn ? 'items-end' : 'items-start'} max-w-full">
                        ${!isOwn ? `
                            <div class="text-xs font-medium text-gray-600 mb-1 px-1">
                                ${message.user.name}
                            </div>
                        ` : ''}

                        ${message.reply_to ? `
                            <div class="mb-1 ${isOwn ? 'mr-2' : 'ml-2'}">
                                <div class="bg-gray-100 border-l-2 ${isOwn ? 'border-blue-500' : 'border-gray-400'} rounded-r-lg px-3 py-2 text-xs max-w-full">
                                    <div class="font-medium text-gray-600 mb-1">
                                        ${message.reply_to.user.name}
                                    </div>
                                    <div class="text-gray-700 line-clamp-2">
                                        ${message.reply_to.content.substring(0, 80)}${message.reply_to.content.length > 80 ? '...' : ''}
                                    </div>
                                </div>
                            </div>
                        ` : ''}

                        <div class="relative">
                            <div class="${isOwn ? 'bg-blue-500 text-white rounded-2xl rounded-br-md' : 'bg-white text-gray-900 rounded-2xl rounded-bl-md shadow-sm border border-gray-200'} px-4 py-2 relative">
                                <div class="text-sm whitespace-pre-wrap break-words leading-relaxed">
                                    ${message.content}
                                </div>

                                <div class="flex items-center justify-end space-x-1 space-x-reverse mt-1 text-xs ${isOwn ? 'text-blue-100' : 'text-gray-500'}">
                                    <span>${time}</span>
                                    ${isOwn ? `
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 ${isOwn ? 'text-blue-200' : 'text-gray-400'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <svg class="w-4 h-4 ${isOwn ? 'text-blue-100' : 'text-gray-300'} -mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Setup message form with edit support
function setupMessageForm() {
    const form = document.getElementById('message-form');
    const input = document.getElementById('message-input');

    form.addEventListener('submit', async (e) => {
        e.preventDefault();

        const content = input.value.trim();
        if (!content) return;

        const editingId = input.dataset.editingId;
        const replyTo = document.getElementById('reply-to').value;

        try {
            let response;

            if (editingId) {
                // Edit existing message
                response = await fetch(`/messages/${editingId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken,
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        content: content
                    })
                });
            } else {
                // Send new message
                response = await fetch(`/chat-rooms/${chatRoomId}/messages`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken,
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        content: content,
                        reply_to: replyTo || null
                    })
                });
            }

            if (response.ok) {
                const data = await response.json();
                input.value = '';
                cancelReply();
                resetMessageForm();

                if (editingId) {
                    // Update existing message in DOM
                    updateMessageInDOM(editingId, data.message);
                    showNotification('پیام ویرایش شد!');
                } else {
                    // Add new message
                    appendNewMessages([data.message]);
                }
            } else {
                const error = await response.json();
                alert(error.error || 'خطا در ارسال پیام');
            }
        } catch (error) {
            console.error('Error sending message:', error);
            alert('خطا در ارسال پیام');
        }
    });

    // Auto-resize input and handle Enter key
    input.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            form.dispatchEvent(new Event('submit'));
        }

        // Cancel edit on Escape
        if (e.key === 'Escape' && input.dataset.editingId) {
            resetMessageForm();
        }
    });
}

// Reset message form to normal state
function resetMessageForm() {
    const input = document.getElementById('message-input');
    const submitBtn = document.querySelector('#message-form button[type="submit"]');

    delete input.dataset.editingId;
    input.value = '';

    // Reset button
    submitBtn.innerHTML = `
        <svg class="h-5 w-5 transform group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
        </svg>
    `;
    submitBtn.classList.remove('from-green-500', 'to-emerald-600');
    submitBtn.classList.add('from-indigo-500', 'to-purple-600');
}

// Update message in DOM
function updateMessageInDOM(messageId, updatedMessage) {
    const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
    if (messageElement) {
        // Add update animation
        messageElement.classList.add('animate-pulse-glow');
        setTimeout(() => {
            messageElement.classList.remove('animate-pulse-glow');
        }, 1000);

        // Update content (you might want to re-render the entire message)
        const contentElement = messageElement.querySelector('.whitespace-pre-wrap');
        if (contentElement) {
            contentElement.textContent = updatedMessage.content;
        }
    }
}

// Setup file upload
function setupFileUpload() {
    const fileInput = document.getElementById('file-input');
    
    fileInput.addEventListener('change', async (e) => {
        const file = e.target.files[0];
        if (!file) return;
        
        // Check file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
            alert('حجم فایل نباید بیشتر از ۱۰ مگابایت باشد');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', file);
        formData.append('_token', csrfToken);
        
        try {
            const response = await fetch(`/chat-rooms/${chatRoomId}/upload`, {
                method: 'POST',
                body: formData
            });
            
            if (response.ok) {
                const data = await response.json();
                appendNewMessages([data.message]);
            } else {
                const error = await response.json();
                alert(error.error || 'خطا در آپلود فایل');
            }
        } catch (error) {
            console.error('Error uploading file:', error);
            alert('خطا در آپلود فایل');
        }
        
        // Reset file input
        fileInput.value = '';
    });
}

// Reply to message with animation
function replyToMessage(messageId, content) {
    document.getElementById('reply-to').value = messageId;
    document.getElementById('reply-content').textContent = content.substring(0, 100) + (content.length > 100 ? '...' : '');

    const replyPreview = document.getElementById('reply-preview');
    replyPreview.classList.remove('hidden');
    replyPreview.classList.add('animate-fade-in');

    document.getElementById('message-input').focus();

    // Hide emoji picker if open
    document.getElementById('emoji-picker').classList.add('hidden');
}

// Cancel reply with animation
function cancelReply() {
    const replyPreview = document.getElementById('reply-preview');
    replyPreview.classList.add('opacity-0', 'scale-95');

    setTimeout(() => {
        document.getElementById('reply-to').value = '';
        replyPreview.classList.add('hidden');
        replyPreview.classList.remove('opacity-0', 'scale-95', 'animate-fade-in');
    }, 200);
}

// Edit message
function editMessage(messageId, content) {
    const input = document.getElementById('message-input');
    input.value = content;
    input.focus();

    // Store message ID for editing
    input.dataset.editingId = messageId;

    // Change button text
    const submitBtn = document.querySelector('#message-form button[type="submit"]');
    submitBtn.innerHTML = `
        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
        </svg>
    `;
    submitBtn.classList.remove('from-indigo-500', 'to-purple-600');
    submitBtn.classList.add('from-green-500', 'to-emerald-600');
}

// Delete message
async function deleteMessage(messageId) {
    if (!confirm('آیا مطمئن هستید که می‌خواهید این پیام را حذف کنید؟')) {
        return;
    }

    try {
        const response = await fetch(`/messages/${messageId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': csrfToken,
                'Accept': 'application/json',
            }
        });

        if (response.ok) {
            // Add fade out animation to message
            const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
            if (messageElement) {
                messageElement.classList.add('opacity-0', 'scale-95');
                setTimeout(() => {
                    messageElement.remove();
                }, 300);
            }
        } else {
            const error = await response.json();
            alert(error.error || 'خطا در حذف پیام');
        }
    } catch (error) {
        console.error('Error deleting message:', error);
        alert('خطا در حذف پیام');
    }
}

// Show reaction picker
function showReactionPicker(messageId) {
    // Simple reaction implementation
    const reactions = ['👍', '❤️', '😂', '😮', '😢', '😡'];
    const reaction = prompt('واکنش خود را انتخاب کنید:\n' + reactions.join(' '));

    if (reaction && reactions.includes(reaction)) {
        // Here you would send the reaction to the server
        console.log(`Reaction ${reaction} for message ${messageId}`);
        // For now, just show a notification
        showNotification(`واکنش ${reaction} ثبت شد!`);
    }
}

// Show notification
function showNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-fade-in';
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.add('opacity-0');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// Scroll to bottom
function scrollToBottom() {
    const container = document.getElementById('messages-container');
    container.scrollTop = container.scrollHeight;
}

// Check if scrolled to bottom
function isScrolledToBottom() {
    const container = document.getElementById('messages-container');
    return container.scrollTop + container.clientHeight >= container.scrollHeight - 10;
}

// Toggle members list
function toggleMembersList() {
    const sidebar = document.getElementById('members-sidebar');
    sidebar.classList.toggle('hidden');
}

// Toggle room info
function toggleRoomInfo() {
    const modal = document.getElementById('room-info-modal');
    modal.classList.toggle('hidden');
}

// Copy room code
function copyRoomCode() {
    const roomCode = document.querySelector('#room-info-modal code').textContent;
    navigator.clipboard.writeText(roomCode).then(() => {
        alert('کد روم کپی شد');
    });
}

// Mark user as online
async function markUserOnline() {
    try {
        await fetch('/api/user/online', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': csrfToken,
                'Accept': 'application/json',
            }
        });
    } catch (error) {
        console.error('Error marking user online:', error);
    }
}

// Mark user as offline
async function markUserOffline() {
    try {
        await fetch('/api/user/offline', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': csrfToken,
                'Accept': 'application/json',
            }
        });
    } catch (error) {
        console.error('Error marking user offline:', error);
    }
}
