// Chat functionality
let socket = null;
let reconnectAttempts = 0;
const maxReconnectAttempts = 5;
let isConnected = false;

// Initialize chat
function initializeChat() {
    setupMessageForm();
    setupFileUpload();
    scrollToBottom();
    connectWebSocket();
    
    // Mark user as online
    markUserOnline();
    
    // Setup periodic online status update
    setInterval(markUserOnline, 30000); // Every 30 seconds
    
    // Setup beforeunload to mark user offline
    window.addEventListener('beforeunload', markUserOffline);
}

// WebSocket connection
function connectWebSocket() {
    try {
        // For development, we'll use polling instead of WebSocket initially
        startPolling();
    } catch (error) {
        console.error('WebSocket connection failed:', error);
        startPolling();
    }
}

// Polling fallback for real-time updates
function startPolling() {
    setInterval(fetchNewMessages, 3000); // Poll every 3 seconds
}

// Fetch new messages
async function fetchNewMessages() {
    try {
        const lastMessageId = getLastMessageId();
        const response = await fetch(`/chat-rooms/${chatRoomId}/messages?after=${lastMessageId}`, {
            headers: {
                'X-CSRF-TOKEN': csrfToken,
                'Accept': 'application/json',
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.messages && data.messages.length > 0) {
                appendNewMessages(data.messages);
            }
        }
    } catch (error) {
        console.error('Error fetching messages:', error);
    }
}

// Get last message ID
function getLastMessageId() {
    const messages = document.querySelectorAll('.message-item');
    if (messages.length > 0) {
        const lastMessage = messages[messages.length - 1];
        return lastMessage.dataset.messageId || 0;
    }
    return 0;
}

// Append new messages
function appendNewMessages(messages) {
    const container = document.getElementById('messages-container');
    const wasAtBottom = isScrolledToBottom();
    
    messages.forEach(message => {
        if (!document.querySelector(`[data-message-id="${message.id}"]`)) {
            const messageHtml = createMessageElement(message);
            container.insertAdjacentHTML('beforeend', messageHtml);
        }
    });
    
    if (wasAtBottom) {
        scrollToBottom();
    }
}

// Create message element HTML
function createMessageElement(message) {
    const isOwn = message.user_id === currentUserId;
    const time = new Date(message.created_at).toLocaleTimeString('fa-IR', {
        hour: '2-digit',
        minute: '2-digit'
    });
    
    return `
        <div class="message-item" data-message-id="${message.id}">
            <div class="flex ${isOwn ? 'justify-end' : 'justify-start'} group">
                <div class="flex ${isOwn ? 'flex-row-reverse' : 'flex-row'} items-end space-x-2 space-x-reverse max-w-xs lg:max-w-md">
                    ${!isOwn ? `
                        <div class="flex-shrink-0">
                            <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                                <span class="text-sm font-medium text-gray-700">
                                    ${message.user.name.charAt(0)}
                                </span>
                            </div>
                        </div>
                    ` : ''}
                    
                    <div class="flex flex-col ${isOwn ? 'items-end' : 'items-start'}">
                        ${!isOwn ? `
                            <span class="text-xs text-gray-500 mb-1 ${isOwn ? 'mr-2' : 'ml-2'}">
                                ${message.user.name}
                            </span>
                        ` : ''}
                        
                        ${message.reply_to ? `
                            <div class="mb-2 p-2 bg-gray-100 rounded-lg border-r-4 border-gray-400 text-sm max-w-full">
                                <div class="text-xs text-gray-600 mb-1">
                                    پاسخ به ${message.reply_to.user.name}:
                                </div>
                                <div class="text-gray-800 truncate">
                                    ${message.reply_to.content.substring(0, 50)}${message.reply_to.content.length > 50 ? '...' : ''}
                                </div>
                            </div>
                        ` : ''}
                        
                        <div class="relative">
                            <div class="px-4 py-2 rounded-lg ${isOwn ? 'bg-blue-600 text-white' : 'bg-white border border-gray-200'}">
                                <div class="text-sm whitespace-pre-wrap break-words">
                                    ${message.content}
                                </div>
                            </div>
                            
                            <div class="absolute top-0 ${isOwn ? 'left-0 -ml-20' : 'right-0 -mr-20'} opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                <div class="flex items-center space-x-1 space-x-reverse bg-white border border-gray-200 rounded-lg shadow-sm p-1">
                                    <button onclick="replyToMessage(${message.id}, '${message.content.replace(/'/g, "\\'")}')">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-2 space-x-reverse mt-1 text-xs text-gray-500 ${isOwn ? 'mr-2' : 'ml-2'}">
                            <span>${time}</span>
                            ${isOwn ? '<span>• ارسال شده</span>' : ''}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Setup message form
function setupMessageForm() {
    const form = document.getElementById('message-form');
    const input = document.getElementById('message-input');
    
    form.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const content = input.value.trim();
        if (!content) return;
        
        const replyTo = document.getElementById('reply-to').value;
        
        try {
            const response = await fetch(`/chat-rooms/${chatRoomId}/messages`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    content: content,
                    reply_to: replyTo || null
                })
            });
            
            if (response.ok) {
                const data = await response.json();
                input.value = '';
                cancelReply();
                
                // Add message immediately for better UX
                appendNewMessages([data.message]);
            } else {
                const error = await response.json();
                alert(error.error || 'خطا در ارسال پیام');
            }
        } catch (error) {
            console.error('Error sending message:', error);
            alert('خطا در ارسال پیام');
        }
    });
    
    // Auto-resize input and handle Enter key
    input.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            form.dispatchEvent(new Event('submit'));
        }
    });
}

// Setup file upload
function setupFileUpload() {
    const fileInput = document.getElementById('file-input');
    
    fileInput.addEventListener('change', async (e) => {
        const file = e.target.files[0];
        if (!file) return;
        
        // Check file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
            alert('حجم فایل نباید بیشتر از ۱۰ مگابایت باشد');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', file);
        formData.append('_token', csrfToken);
        
        try {
            const response = await fetch(`/chat-rooms/${chatRoomId}/upload`, {
                method: 'POST',
                body: formData
            });
            
            if (response.ok) {
                const data = await response.json();
                appendNewMessages([data.message]);
            } else {
                const error = await response.json();
                alert(error.error || 'خطا در آپلود فایل');
            }
        } catch (error) {
            console.error('Error uploading file:', error);
            alert('خطا در آپلود فایل');
        }
        
        // Reset file input
        fileInput.value = '';
    });
}

// Reply to message
function replyToMessage(messageId, content) {
    document.getElementById('reply-to').value = messageId;
    document.getElementById('reply-content').textContent = content.substring(0, 100) + (content.length > 100 ? '...' : '');
    document.getElementById('reply-preview').classList.remove('hidden');
    document.getElementById('message-input').focus();
}

// Cancel reply
function cancelReply() {
    document.getElementById('reply-to').value = '';
    document.getElementById('reply-preview').classList.add('hidden');
}

// Scroll to bottom
function scrollToBottom() {
    const container = document.getElementById('messages-container');
    container.scrollTop = container.scrollHeight;
}

// Check if scrolled to bottom
function isScrolledToBottom() {
    const container = document.getElementById('messages-container');
    return container.scrollTop + container.clientHeight >= container.scrollHeight - 10;
}

// Toggle members list
function toggleMembersList() {
    const sidebar = document.getElementById('members-sidebar');
    sidebar.classList.toggle('hidden');
}

// Toggle room info
function toggleRoomInfo() {
    const modal = document.getElementById('room-info-modal');
    modal.classList.toggle('hidden');
}

// Copy room code
function copyRoomCode() {
    const roomCode = document.querySelector('#room-info-modal code').textContent;
    navigator.clipboard.writeText(roomCode).then(() => {
        alert('کد روم کپی شد');
    });
}

// Mark user as online
async function markUserOnline() {
    try {
        await fetch('/api/user/online', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': csrfToken,
                'Accept': 'application/json',
            }
        });
    } catch (error) {
        console.error('Error marking user online:', error);
    }
}

// Mark user as offline
async function markUserOffline() {
    try {
        await fetch('/api/user/offline', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': csrfToken,
                'Accept': 'application/json',
            }
        });
    } catch (error) {
        console.error('Error marking user offline:', error);
    }
}
